"""
QuizAIGen Exception Classes

This module defines custom exceptions for the QuizAIGen library.
"""

from typing import Optional, Any


class QuizAIGenError(Exception):
    """Base exception class for QuizAIGen library."""
    
    def __init__(self, message: str, details: Optional[dict] = None):
        """
        Initialize QuizAIGenError.
        
        Args:
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message)
        self.message = message
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.details:
            return f"{self.message} | Details: {self.details}"
        return self.message


class ModelLoadError(QuizAIGenError):
    """Exception raised when model loading fails."""
    
    def __init__(self, model_name: str, message: str, details: Optional[dict] = None):
        """
        Initialize ModelLoadError.
        
        Args:
            model_name: Name of the model that failed to load
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.model_name = model_name
    
    def __str__(self) -> str:
        base_msg = f"Failed to load model '{self.model_name}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class ProcessingError(QuizAIGenError):
    """Exception raised during text processing or question generation."""
    
    def __init__(self, stage: str, message: str, details: Optional[dict] = None):
        """
        Initialize ProcessingError.
        
        Args:
            stage: Processing stage where error occurred
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.stage = stage
    
    def __str__(self) -> str:
        base_msg = f"Processing error in stage '{self.stage}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class ValidationError(QuizAIGenError):
    """Exception raised when input validation fails."""
    
    def __init__(self, field: str, value: Any, message: str, details: Optional[dict] = None):
        """
        Initialize ValidationError.
        
        Args:
            field: Field name that failed validation
            value: Value that failed validation
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.field = field
        self.value = value
    
    def __str__(self) -> str:
        base_msg = f"Validation error for field '{self.field}' with value '{self.value}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class ConfigurationError(QuizAIGenError):
    """Exception raised when configuration is invalid."""
    
    def __init__(self, config_key: str, message: str, details: Optional[dict] = None):
        """
        Initialize ConfigurationError.
        
        Args:
            config_key: Configuration key that is invalid
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.config_key = config_key
    
    def __str__(self) -> str:
        base_msg = f"Configuration error for key '{self.config_key}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class InferenceError(QuizAIGenError):
    """Exception raised during model inference."""
    
    def __init__(self, model_name: str, message: str, details: Optional[dict] = None):
        """
        Initialize InferenceError.
        
        Args:
            model_name: Name of the model that failed during inference
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.model_name = model_name
    
    def __str__(self) -> str:
        base_msg = f"Inference error with model '{self.model_name}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class ExportError(QuizAIGenError):
    """Exception raised during question export."""
    
    def __init__(self, export_format: str, message: str, details: Optional[dict] = None):
        """
        Initialize ExportError.
        
        Args:
            export_format: Export format that failed
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.export_format = export_format
    
    def __str__(self) -> str:
        base_msg = f"Export error for format '{self.export_format}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg


class InputError(QuizAIGenError):
    """Exception raised when input processing fails."""
    
    def __init__(self, input_type: str, message: str, details: Optional[dict] = None):
        """
        Initialize InputError.
        
        Args:
            input_type: Type of input that failed processing
            message: Error message
            details: Optional dictionary with additional error details
        """
        super().__init__(message, details)
        self.input_type = input_type
    
    def __str__(self) -> str:
        base_msg = f"Input processing error for type '{self.input_type}': {self.message}"
        if self.details:
            return f"{base_msg} | Details: {self.details}"
        return base_msg
