"""
QuizAIGen: AI-Powered Question Generation Library

A modular, extensible Python library for generating various types of questions
from text using state-of-the-art transformer models.

Author: QuizAIGen Team
License: MIT
Version: 0.1.0
"""

__version__ = "0.1.0"
__author__ = "QuizAIGen Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Core API imports for easy access
from .api.question_generator import QuestionGenerator
from .export.export_manager import ExportManager
from .inputs.text_processor import TextProcessor

# Exception imports
from .core.exceptions import (
    QuizAIGenError,
    ProcessingError,
    ValidationError,
    InputError,
    ModelLoadError
)

# Configuration
from .core.config import Config

__all__ = [
    # Main API classes
    'QuestionGenerator',
    'ExportManager',
    'TextProcessor',

    # Configuration
    'Config',

    # Exceptions
    'QuizAIGenError',
    'ProcessingError',
    'ValidationError',
    'InputError',
    'ModelLoadError',

    # Version info
    '__version__',
    '__author__',
    '__email__',
    '__license__'
]

# Library-level configuration
DEFAULT_CONFIG = {
    'models': {
        'mcq': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        },
        'boolean': {
            'name': 'bert-base-uncased',
            'cache': True,
            'max_length': 512
        },
        'short_answer': {
            'name': 't5-base',
            'cache': True,
            'max_length': 512
        }
    },
    'processing': {
        'max_questions': 50,
        'min_quality_score': 0.7,
        'remove_duplicates': True,
        'language': 'en'
    },
    'export': {
        'default_format': 'json',
        'include_metadata': True,
        'pretty_print': True
    }
}

def get_version():
    """Get the current version of QuizAIGen."""
    return __version__

def get_config():
    """Get the default configuration."""
    return DEFAULT_CONFIG.copy()
