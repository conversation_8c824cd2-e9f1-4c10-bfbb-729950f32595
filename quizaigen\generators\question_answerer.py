"""
Question Answerer

This module answers questions based on provided text context.
"""

import re
import random
from typing import List, Dict, Any, Optional, Union, Tuple

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, clean_text


class QuestionAnswerer(BaseQuestionGenerator):
    """Generator for answering questions based on text context."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize QuestionAnswerer.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Keywords that indicate boolean questions
        self.boolean_indicators = [
            'is', 'are', 'was', 'were', 'does', 'do', 'did', 'can', 'could',
            'will', 'would', 'should', 'has', 'have', 'had'
        ]
        
        # Positive and negative indicators for boolean answers
        self.positive_indicators = [
            'yes', 'true', 'correct', 'right', 'indeed', 'certainly', 'definitely',
            'absolutely', 'of course', 'naturally', 'obviously'
        ]
        
        self.negative_indicators = [
            'no', 'false', 'incorrect', 'wrong', 'not', 'never', 'none',
            'neither', 'impossible', 'unlikely', 'doubtful'
        ]
        
        self.log_info("Initialized QuestionAnswerer")
    
    def answer_questions(self, context: str, questions: List[Union[str, Question]]) -> List[Dict[str, Any]]:
        """
        Answer a list of questions based on the provided context.
        
        Args:
            context: Text context to search for answers
            questions: List of questions (strings or Question objects)
        
        Returns:
            List of answer dictionaries
        """
        answers = []
        
        for question in questions:
            if isinstance(question, Question):
                question_text = question.question
                question_type = question.type
            else:
                question_text = question
                question_type = self._detect_question_type(question_text)
            
            answer_data = self._answer_single_question(context, question_text, question_type)
            
            if isinstance(question, Question):
                answer_data['original_question'] = question
            
            answers.append(answer_data)
        
        self.log_info(f"Answered {len(answers)} questions")
        return answers
    
    def _answer_single_question(self, context: str, question: str, 
                              question_type: str) -> Dict[str, Any]:
        """
        Answer a single question based on context.
        
        Args:
            context: Text context
            question: Question text
            question_type: Type of question
        
        Returns:
            Answer data dictionary
        """
        if question_type == 'boolean':
            return self._answer_boolean_question(context, question)
        else:
            return self._answer_extractive_question(context, question)
    
    def _detect_question_type(self, question: str) -> str:
        """
        Detect if a question is boolean or extractive.
        
        Args:
            question: Question text
        
        Returns:
            Question type ('boolean' or 'extractive')
        """
        question_lower = question.lower().strip()
        
        # Check if it starts with boolean indicators
        first_word = question_lower.split()[0] if question_lower.split() else ""
        
        if first_word in self.boolean_indicators:
            return 'boolean'
        
        # Check for yes/no question patterns
        if any(pattern in question_lower for pattern in [
            'is it true', 'is it correct', 'can we say', 'does it mean'
        ]):
            return 'boolean'
        
        return 'extractive'
    
    def _answer_boolean_question(self, context: str, question: str) -> Dict[str, Any]:
        """
        Answer a boolean (yes/no) question.
        
        Args:
            context: Text context
            question: Boolean question
        
        Returns:
            Boolean answer data
        """
        # Extract key terms from the question
        key_terms = self._extract_key_terms(question)
        
        # Find relevant sentences in context
        relevant_sentences = self._find_relevant_sentences(context, key_terms)
        
        if not relevant_sentences:
            return {
                'question': question,
                'answer': 'Unknown',
                'confidence': 0.1,
                'answer_type': 'boolean',
                'evidence': [],
                'reasoning': 'No relevant information found in context'
            }
        
        # Analyze sentences for positive/negative evidence
        positive_score = 0
        negative_score = 0
        evidence_sentences = []
        
        for sentence, relevance_score in relevant_sentences:
            sentence_lower = sentence.lower()
            
            # Count positive indicators
            pos_count = sum(1 for indicator in self.positive_indicators 
                          if indicator in sentence_lower)
            
            # Count negative indicators  
            neg_count = sum(1 for indicator in self.negative_indicators
                          if indicator in sentence_lower)
            
            # Weight by relevance
            weighted_pos = pos_count * relevance_score
            weighted_neg = neg_count * relevance_score
            
            positive_score += weighted_pos
            negative_score += weighted_neg
            
            if weighted_pos > 0 or weighted_neg > 0:
                evidence_sentences.append(sentence)
        
        # Determine answer
        if positive_score > negative_score:
            answer = 'Yes'
            confidence = min(0.9, 0.5 + (positive_score - negative_score) * 0.1)
        elif negative_score > positive_score:
            answer = 'No'
            confidence = min(0.9, 0.5 + (negative_score - positive_score) * 0.1)
        else:
            answer = 'Unknown'
            confidence = 0.3
        
        return {
            'question': question,
            'answer': answer,
            'confidence': confidence,
            'answer_type': 'boolean',
            'evidence': evidence_sentences[:3],  # Top 3 evidence sentences
            'reasoning': f'Positive indicators: {positive_score}, Negative indicators: {negative_score}'
        }
    
    def _answer_extractive_question(self, context: str, question: str) -> Dict[str, Any]:
        """
        Answer an extractive question by finding relevant text spans.
        
        Args:
            context: Text context
            question: Extractive question
        
        Returns:
            Extractive answer data
        """
        # Extract key terms from the question
        key_terms = self._extract_key_terms(question)
        
        # Find relevant sentences
        relevant_sentences = self._find_relevant_sentences(context, key_terms)
        
        if not relevant_sentences:
            return {
                'question': question,
                'answer': 'No answer found',
                'confidence': 0.1,
                'answer_type': 'extractive',
                'evidence': [],
                'reasoning': 'No relevant information found in context'
            }
        
        # Extract the best answer span
        best_answer = self._extract_answer_span(question, relevant_sentences)
        
        return best_answer
    
    def _extract_key_terms(self, question: str) -> List[str]:
        """
        Extract key terms from a question.
        
        Args:
            question: Question text
        
        Returns:
            List of key terms
        """
        # Remove question words and common words
        question_words = {
            'what', 'when', 'where', 'who', 'why', 'how', 'which', 'whose',
            'is', 'are', 'was', 'were', 'do', 'does', 'did', 'can', 'could',
            'will', 'would', 'should', 'has', 'have', 'had', 'the', 'a', 'an',
            'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'
        }
        
        # Clean and tokenize
        clean_question = re.sub(r'[^\w\s]', '', question.lower())
        words = clean_question.split()
        
        # Filter out question words and short words
        key_terms = [word for word in words 
                    if word not in question_words and len(word) > 2]
        
        return key_terms
    
    def _find_relevant_sentences(self, context: str, key_terms: List[str]) -> List[Tuple[str, float]]:
        """
        Find sentences in context that are relevant to the key terms.
        
        Args:
            context: Text context
            key_terms: List of key terms to search for
        
        Returns:
            List of (sentence, relevance_score) tuples
        """
        sentences = extract_sentences(context)
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            
            # Count how many key terms appear in the sentence
            term_matches = sum(1 for term in key_terms if term in sentence_lower)
            
            if term_matches > 0:
                # Calculate relevance score
                relevance_score = term_matches / len(key_terms)
                
                # Bonus for exact phrase matches
                question_phrase = ' '.join(key_terms[:3])  # First 3 terms as phrase
                if question_phrase in sentence_lower:
                    relevance_score += 0.3
                
                relevant_sentences.append((sentence, relevance_score))
        
        # Sort by relevance score
        relevant_sentences.sort(key=lambda x: x[1], reverse=True)
        
        return relevant_sentences[:5]  # Top 5 most relevant
    
    def _extract_answer_span(self, question: str, relevant_sentences: List[Tuple[str, float]]) -> Dict[str, Any]:
        """
        Extract the best answer span from relevant sentences.
        
        Args:
            question: Original question
            relevant_sentences: List of relevant sentences with scores
        
        Returns:
            Answer data dictionary
        """
        if not relevant_sentences:
            return {
                'question': question,
                'answer': 'No answer found',
                'confidence': 0.1,
                'answer_type': 'extractive',
                'evidence': [],
                'reasoning': 'No relevant sentences found'
            }
        
        # For now, use the most relevant sentence as the answer
        best_sentence, best_score = relevant_sentences[0]
        
        # Try to extract a more specific answer span
        answer_span = self._find_answer_in_sentence(question, best_sentence)
        
        if answer_span:
            answer = answer_span
            confidence = min(0.9, 0.4 + best_score * 0.5)
        else:
            answer = best_sentence
            confidence = min(0.8, 0.3 + best_score * 0.4)
        
        return {
            'question': question,
            'answer': answer,
            'confidence': confidence,
            'answer_type': 'extractive',
            'evidence': [sent for sent, _ in relevant_sentences[:3]],
            'reasoning': f'Best match score: {best_score:.2f}'
        }
    
    def _find_answer_in_sentence(self, question: str, sentence: str) -> Optional[str]:
        """
        Find a specific answer span within a sentence.
        
        Args:
            question: Original question
            sentence: Sentence to search in
        
        Returns:
            Answer span or None
        """
        question_lower = question.lower()
        sentence_lower = sentence.lower()
        
        # Simple patterns for common question types
        if question_lower.startswith('what is'):
            # Look for "X is Y" patterns
            is_match = re.search(r'(\w+(?:\s+\w+)*)\s+is\s+([^.!?]+)', sentence_lower)
            if is_match:
                return is_match.group(2).strip()
        
        elif question_lower.startswith('when'):
            # Look for dates and time expressions
            date_pattern = r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}|(?:January|February|March|April|May|June|July|August|September|October|November|December)\s+\d{1,2},?\s+\d{4})\b'
            date_match = re.search(date_pattern, sentence)
            if date_match:
                return date_match.group(0)
        
        elif question_lower.startswith('where'):
            # Look for location indicators
            location_pattern = r'\b(?:in|at|on|near|from|to)\s+([A-Z][a-zA-Z\s]+?)(?:\s|,|\.)'
            location_match = re.search(location_pattern, sentence)
            if location_match:
                return location_match.group(1).strip()
        
        # If no specific pattern matches, return None to use full sentence
        return None
    
    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        This method is not used for answering but required by base class.
        
        Args:
            text: Input text (not used)
            num_questions: Number of questions (not used)
            **kwargs: Additional parameters (not used)
        
        Returns:
            Empty list (answering works on existing questions)
        """
        self.log_warning("_generate_questions_impl called on QuestionAnswerer - use answer_questions instead")
        return []
