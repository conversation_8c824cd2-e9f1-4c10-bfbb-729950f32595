"""
URL Processor

This module handles processing of web URLs to extract text content.
"""

import re
import time
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse, urljoin
from urllib.robotparser import RobotFileParser

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

from .base import BaseInputProcessor


class URLProcessor(BaseInputProcessor):
    """Processor for web URLs."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize URLProcessor.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Check required libraries
        if not REQUESTS_AVAILABLE:
            raise ImportError("requests library is required for URL processing")
        
        if not BS4_AVAILABLE:
            raise ImportError("beautifulsoup4 library is required for URL processing")
        
        # Configuration
        self.timeout = self.config.get('timeout', 30)
        self.max_content_length = self.config.get('max_content_length', 10 * 1024 * 1024)  # 10MB
        self.user_agent = self.config.get('user_agent', 'QuizAIGen/1.0 (+https://github.com/quizaigen)')
        self.respect_robots = self.config.get('respect_robots', True)
        self.delay_between_requests = self.config.get('delay_between_requests', 1.0)
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        })
        
        self.log_info("Initialized URLProcessor")
    
    def process(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Process a URL and extract text content.
        
        Args:
            url: URL to process
            **kwargs: Additional processing options
        
        Returns:
            Dictionary containing extracted text and metadata
        """
        if not self._validate_url(url):
            raise ValueError(f"Invalid URL: {url}")
        
        self.log_info(f"Processing URL: {url}")
        
        # Check robots.txt if enabled
        if self.respect_robots and not self._check_robots_txt(url):
            raise PermissionError(f"Robots.txt disallows access to: {url}")
        
        try:
            # Fetch the content
            response = self._fetch_url(url, **kwargs)
            
            if not response['success']:
                return response
            
            # Extract text from HTML
            text_data = self._extract_text_from_html(response['content'], url)
            
            # Combine results
            result = {
                'text': text_data['text'],
                'metadata': {
                    **response['metadata'],
                    **text_data['metadata']
                },
                'success': True
            }
            
            return result
        
        except Exception as e:
            self.log_error(f"Error processing URL {url}: {str(e)}")
            return {
                'text': '',
                'metadata': {'url': url, 'error': str(e)},
                'success': False,
                'error': str(e)
            }
    
    def _validate_url(self, url: str) -> bool:
        """
        Validate URL format and scheme.
        
        Args:
            url: URL to validate
        
        Returns:
            True if URL is valid
        """
        try:
            parsed = urlparse(url)
            return parsed.scheme in ['http', 'https'] and parsed.netloc
        except Exception:
            return False
    
    def _check_robots_txt(self, url: str) -> bool:
        """
        Check if URL is allowed by robots.txt.
        
        Args:
            url: URL to check
        
        Returns:
            True if access is allowed
        """
        try:
            parsed_url = urlparse(url)
            robots_url = f"{parsed_url.scheme}://{parsed_url.netloc}/robots.txt"
            
            rp = RobotFileParser()
            rp.set_url(robots_url)
            rp.read()
            
            return rp.can_fetch(self.user_agent, url)
        
        except Exception as e:
            self.log_warning(f"Could not check robots.txt for {url}: {str(e)}")
            return True  # Allow if robots.txt check fails
    
    def _fetch_url(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Fetch content from URL.
        
        Args:
            url: URL to fetch
            **kwargs: Additional options
        
        Returns:
            Dictionary with content and metadata
        """
        metadata = {
            'url': url,
            'fetch_time': time.time(),
            'method': 'GET'
        }
        
        try:
            # Add delay between requests
            time.sleep(self.delay_between_requests)
            
            # Make request
            response = self.session.get(
                url,
                timeout=self.timeout,
                stream=True,
                allow_redirects=True
            )
            
            # Check content length
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) > self.max_content_length:
                raise ValueError(f"Content too large: {content_length} bytes")
            
            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' not in content_type and 'application/xhtml' not in content_type:
                self.log_warning(f"Unexpected content type: {content_type}")
            
            # Read content with size limit
            content = b''
            for chunk in response.iter_content(chunk_size=8192):
                content += chunk
                if len(content) > self.max_content_length:
                    raise ValueError(f"Content too large: {len(content)} bytes")
            
            # Update metadata
            metadata.update({
                'status_code': response.status_code,
                'content_type': content_type,
                'content_length': len(content),
                'encoding': response.encoding or 'utf-8',
                'final_url': response.url,
                'redirects': len(response.history)
            })
            
            # Check status code
            response.raise_for_status()
            
            # Decode content
            try:
                text_content = content.decode(response.encoding or 'utf-8')
            except UnicodeDecodeError:
                # Try common encodings
                for encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        text_content = content.decode(encoding)
                        metadata['encoding'] = encoding
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise UnicodeDecodeError("Could not decode content")
            
            return {
                'content': text_content,
                'metadata': metadata,
                'success': True
            }
        
        except Exception as e:
            self.log_error(f"Error fetching URL {url}: {str(e)}")
            return {
                'content': '',
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }
    
    def _extract_text_from_html(self, html_content: str, url: str) -> Dict[str, Any]:
        """
        Extract text content from HTML.
        
        Args:
            html_content: HTML content
            url: Original URL for context
        
        Returns:
            Dictionary with extracted text and metadata
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header", "aside"]):
                script.decompose()
            
            # Extract title
            title = soup.find('title')
            title_text = title.get_text().strip() if title else ''
            
            # Extract main content
            # Try to find main content areas
            main_content = None
            for selector in ['main', 'article', '[role="main"]', '.content', '#content']:
                main_content = soup.select_one(selector)
                if main_content:
                    break
            
            if not main_content:
                # Fall back to body
                main_content = soup.find('body') or soup
            
            # Extract text from paragraphs and headings
            text_elements = main_content.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'div'])
            
            text_parts = []
            for element in text_elements:
                text = element.get_text().strip()
                if text and len(text) > 10:  # Filter out very short text
                    text_parts.append(text)
            
            # Combine text
            full_text = '\n\n'.join(text_parts)
            
            # Clean text
            cleaned_text = self._clean_extracted_text(full_text)
            
            metadata = {
                'title': title_text,
                'text_elements_found': len(text_parts),
                'character_count': len(cleaned_text),
                'word_count': len(cleaned_text.split()),
                'extraction_method': 'BeautifulSoup'
            }
            
            return {
                'text': cleaned_text,
                'metadata': metadata
            }
        
        except Exception as e:
            self.log_error(f"Error extracting text from HTML: {str(e)}")
            return {
                'text': '',
                'metadata': {'error': str(e)}
            }
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean extracted text from web pages.
        
        Args:
            text: Raw extracted text
        
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r' +', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Remove common web artifacts
        text = re.sub(r'(Cookie|Privacy) Policy.*?(?=\n\n|\Z)', '', text, flags=re.IGNORECASE | re.DOTALL)
        text = re.sub(r'Terms of Service.*?(?=\n\n|\Z)', '', text, flags=re.IGNORECASE | re.DOTALL)
        
        # Clean up lines
        lines = [line.strip() for line in text.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        text = '\n\n'.join(lines)
        text = text.strip()
        
        return text
    
    def process_url(self, url: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process a single URL.
        
        Args:
            url: URL to process
            **kwargs: Additional options
        
        Returns:
            Processing result
        """
        return self.process(url, **kwargs)
    
    def process_multiple_urls(self, urls: List[str], **kwargs) -> List[Dict[str, Any]]:
        """
        Process multiple URLs.
        
        Args:
            urls: List of URLs to process
            **kwargs: Additional options
        
        Returns:
            List of processing results
        """
        results = []
        
        for url in urls:
            try:
                result = self.process(url, **kwargs)
                results.append(result)
                
                # Add delay between requests
                if len(results) < len(urls):
                    time.sleep(self.delay_between_requests)
            
            except Exception as e:
                self.log_error(f"Error processing URL {url}: {str(e)}")
                results.append({
                    'text': '',
                    'metadata': {'url': url, 'error': str(e)},
                    'success': False,
                    'error': str(e)
                })
        
        return results
