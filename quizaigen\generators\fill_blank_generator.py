"""
Fill-in-the-Blank Question Generator

This module generates fill-in-the-blank questions from input text.
"""

import re
import random
from typing import List, Dict, Any, Optional, Tuple

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, clean_text


class FillBlankGenerator(BaseQuestionGenerator):
    """Generator for fill-in-the-blank questions."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize FillBlankGenerator.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # POS tags that are good candidates for blanks
        self.target_pos_tags = {
            'NOUN', 'PROPN',  # Nouns and proper nouns
            'VERB',           # Verbs
            'ADJ',            # Adjectives
            'NUM',            # Numbers
            'DATE'            # Dates
        }
        
        # Words to avoid making into blanks
        self.avoid_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'
        }
        
        # Minimum word length for blanks
        self.min_word_length = 3
        
        self.log_info("Initialized FillBlankGenerator")
    
    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        Generate fill-in-the-blank questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of fill-in-the-blank questions
        """
        # Extract sentences
        sentences = extract_sentences(text)
        
        if not sentences:
            self.log_warning("No sentences found in text")
            return []
        
        # Filter sentences by length (not too short, not too long)
        suitable_sentences = [
            s for s in sentences 
            if 50 <= len(s) <= 200 and len(s.split()) >= 8
        ]
        
        if not suitable_sentences:
            self.log_warning("No suitable sentences found for fill-in-blank questions")
            return []
        
        questions = []
        used_sentences = set()
        
        for _ in range(min(num_questions, len(suitable_sentences))):
            # Select a sentence that hasn't been used
            available_sentences = [s for s in suitable_sentences if s not in used_sentences]
            if not available_sentences:
                break
            
            sentence = random.choice(available_sentences)
            used_sentences.add(sentence)
            
            # Generate fill-in-blank question from sentence
            question_data = self._create_fill_blank_question(sentence)
            
            if question_data:
                question = Question(
                    question=question_data['question'],
                    answer=question_data['answer'],
                    type='fill_blank',
                    confidence=question_data['confidence'],
                    metadata={
                        'original_sentence': sentence,
                        'blank_position': question_data['blank_position'],
                        'context': question_data.get('context', ''),
                        'difficulty': question_data.get('difficulty', 'medium')
                    }
                )
                questions.append(question)
        
        self.log_info(f"Generated {len(questions)} fill-in-blank questions")
        return questions
    
    def _create_fill_blank_question(self, sentence: str) -> Optional[Dict[str, Any]]:
        """
        Create a fill-in-blank question from a sentence.
        
        Args:
            sentence: Input sentence
        
        Returns:
            Question data dictionary or None if no suitable blank found
        """
        # Find potential blank candidates
        candidates = self._find_blank_candidates(sentence)
        
        if not candidates:
            return None
        
        # Select the best candidate
        best_candidate = self._select_best_candidate(candidates, sentence)
        
        if not best_candidate:
            return None
        
        word, start_pos, end_pos = best_candidate
        
        # Create the question with blank
        question_text = sentence[:start_pos] + "______" + sentence[end_pos:]
        
        # Clean up extra spaces
        question_text = re.sub(r'\s+', ' ', question_text).strip()
        
        # Calculate confidence based on word characteristics
        confidence = self._calculate_blank_confidence(word, sentence)
        
        # Determine difficulty
        difficulty = self._determine_difficulty(word, sentence)
        
        return {
            'question': question_text,
            'answer': word,
            'blank_position': start_pos,
            'confidence': confidence,
            'difficulty': difficulty,
            'context': sentence
        }
    
    def _find_blank_candidates(self, sentence: str) -> List[Tuple[str, int, int]]:
        """
        Find potential words that can be made into blanks.
        
        Args:
            sentence: Input sentence
        
        Returns:
            List of (word, start_position, end_position) tuples
        """
        candidates = []
        
        # Simple word extraction with positions
        words = re.finditer(r'\b\w+\b', sentence)
        
        for match in words:
            word = match.group().lower()
            start_pos = match.start()
            end_pos = match.end()
            
            # Check if word is a good candidate
            if self._is_good_blank_candidate(word, sentence):
                candidates.append((match.group(), start_pos, end_pos))
        
        return candidates
    
    def _is_good_blank_candidate(self, word: str, sentence: str) -> bool:
        """
        Check if a word is a good candidate for a blank.
        
        Args:
            word: Word to check
            sentence: Full sentence context
        
        Returns:
            True if word is a good candidate
        """
        word_lower = word.lower()
        
        # Check minimum length
        if len(word) < self.min_word_length:
            return False
        
        # Avoid common words
        if word_lower in self.avoid_words:
            return False
        
        # Avoid words that are too common
        if word_lower in ['this', 'that', 'these', 'those', 'here', 'there', 'when', 'where']:
            return False
        
        # Prefer nouns, verbs, adjectives, and numbers
        if self._looks_like_noun(word, sentence):
            return True
        
        if self._looks_like_verb(word, sentence):
            return True
        
        if self._looks_like_adjective(word, sentence):
            return True
        
        if self._looks_like_number_or_date(word):
            return True
        
        # Check if it's a proper noun (capitalized)
        if word[0].isupper() and not sentence.strip().startswith(word):
            return True
        
        return False
    
    def _looks_like_noun(self, word: str, sentence: str) -> bool:
        """Check if word appears to be a noun based on context."""
        word_lower = word.lower()
        
        # Common noun patterns
        if word_lower.endswith(('tion', 'sion', 'ment', 'ness', 'ity', 'er', 'or', 'ist')):
            return True
        
        # Check if preceded by articles or adjectives
        word_pattern = r'\b(?:the|a|an|this|that|some|many|few|several|most|all)\s+\w*\s*' + re.escape(word) + r'\b'
        if re.search(word_pattern, sentence, re.IGNORECASE):
            return True
        
        return False
    
    def _looks_like_verb(self, word: str, sentence: str) -> bool:
        """Check if word appears to be a verb based on context."""
        word_lower = word.lower()
        
        # Common verb patterns
        if word_lower.endswith(('ed', 'ing', 'es', 's')):
            return True
        
        # Check if it's in a verb position (after subject)
        # This is a simple heuristic
        words = sentence.split()
        try:
            word_index = words.index(word)
            if word_index > 0 and word_index < len(words) - 1:
                prev_word = words[word_index - 1].lower()
                if prev_word in ['i', 'you', 'he', 'she', 'it', 'we', 'they']:
                    return True
        except ValueError:
            pass
        
        return False
    
    def _looks_like_adjective(self, word: str, sentence: str) -> bool:
        """Check if word appears to be an adjective based on context."""
        word_lower = word.lower()
        
        # Common adjective patterns
        if word_lower.endswith(('ful', 'less', 'ous', 'ive', 'able', 'ible', 'al', 'ic')):
            return True
        
        # Check if it's before a noun
        word_pattern = re.escape(word) + r'\s+\w+'
        if re.search(word_pattern, sentence, re.IGNORECASE):
            return True
        
        return False
    
    def _looks_like_number_or_date(self, word: str) -> bool:
        """Check if word is a number or date."""
        # Check for numbers
        if word.isdigit():
            return True
        
        # Check for years
        if len(word) == 4 and word.isdigit() and 1800 <= int(word) <= 2100:
            return True
        
        # Check for months
        months = ['january', 'february', 'march', 'april', 'may', 'june',
                 'july', 'august', 'september', 'october', 'november', 'december']
        if word.lower() in months:
            return True
        
        return False
    
    def _select_best_candidate(self, candidates: List[Tuple[str, int, int]], 
                             sentence: str) -> Optional[Tuple[str, int, int]]:
        """
        Select the best candidate for a blank from the list.
        
        Args:
            candidates: List of candidate words with positions
            sentence: Original sentence
        
        Returns:
            Best candidate tuple or None
        """
        if not candidates:
            return None
        
        # Score each candidate
        scored_candidates = []
        
        for word, start_pos, end_pos in candidates:
            score = self._score_candidate(word, sentence, start_pos)
            scored_candidates.append((score, word, start_pos, end_pos))
        
        # Sort by score (highest first)
        scored_candidates.sort(reverse=True)
        
        # Return the best candidate
        _, word, start_pos, end_pos = scored_candidates[0]
        return (word, start_pos, end_pos)
    
    def _score_candidate(self, word: str, sentence: str, position: int) -> float:
        """
        Score a candidate word for blank suitability.
        
        Args:
            word: Candidate word
            sentence: Full sentence
            position: Position of word in sentence
        
        Returns:
            Score (higher is better)
        """
        score = 0.0
        word_lower = word.lower()
        
        # Length bonus (longer words are generally better)
        score += len(word) * 0.1
        
        # Position bonus (middle of sentence is better)
        sentence_length = len(sentence)
        relative_position = position / sentence_length
        if 0.2 <= relative_position <= 0.8:
            score += 1.0
        
        # Content word bonus
        if self._looks_like_noun(word, sentence):
            score += 2.0
        elif self._looks_like_verb(word, sentence):
            score += 1.5
        elif self._looks_like_adjective(word, sentence):
            score += 1.0
        
        # Proper noun bonus
        if word[0].isupper() and not sentence.strip().startswith(word):
            score += 1.5
        
        # Number/date bonus
        if self._looks_like_number_or_date(word):
            score += 1.0
        
        # Avoid very common words
        common_words = ['said', 'says', 'made', 'make', 'get', 'got', 'put', 'take', 'took']
        if word_lower in common_words:
            score -= 1.0
        
        return score
    
    def _calculate_blank_confidence(self, word: str, sentence: str) -> float:
        """
        Calculate confidence score for a blank question.
        
        Args:
            word: The word that was blanked
            sentence: Original sentence
        
        Returns:
            Confidence score between 0 and 1
        """
        confidence = 0.5  # Base confidence
        
        # Length factor
        if len(word) >= 5:
            confidence += 0.1
        
        # Content type factor
        if self._looks_like_noun(word, sentence):
            confidence += 0.2
        elif self._looks_like_verb(word, sentence):
            confidence += 0.15
        
        # Proper noun factor
        if word[0].isupper():
            confidence += 0.1
        
        # Sentence quality factor
        if 10 <= len(sentence.split()) <= 20:
            confidence += 0.1
        
        return min(0.95, confidence)
    
    def _determine_difficulty(self, word: str, sentence: str) -> str:
        """
        Determine the difficulty level of a fill-in-blank question.
        
        Args:
            word: The word that was blanked
            sentence: Original sentence
        
        Returns:
            Difficulty level ('easy', 'medium', 'hard')
        """
        # Simple heuristics for difficulty
        word_length = len(word)
        sentence_complexity = len(sentence.split())
        
        if word_length <= 4 and sentence_complexity <= 12:
            return 'easy'
        elif word_length >= 8 or sentence_complexity >= 20:
            return 'hard'
        else:
            return 'medium'
