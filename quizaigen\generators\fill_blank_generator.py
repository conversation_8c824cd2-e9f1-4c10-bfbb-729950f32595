"""
Fill-in-the-Blank Question Generator

This module generates fill-in-the-blank questions from text.
"""

import re
import random
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import Counter

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import extract_sentences, extract_keywords, tokenize_text
from ..utils.validation_utils import validate_text_input, validate_positive_integer


class FillBlankGenerator(BaseQuestionGenerator):
    """Generator for fill-in-the-blank questions."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize FillBlankGenerator.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Words that are good candidates for blanks
        self.target_pos_tags = ['NOUN', 'VERB', 'ADJ', 'ADV']
        
        # Words to avoid making into blanks
        self.avoid_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during',
            'before', 'after', 'above', 'below', 'between', 'among', 'is', 'are',
            'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does',
            'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must'
        }
        
        self.log_info("Initialized FillBlankGenerator")
    
    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        Generate fill-in-the-blank questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of generated questions
        """
        # Extract sentences
        sentences = extract_sentences(text)
        if len(sentences) < 2:
            self.log_warning("Text has too few sentences for fill-blank generation")
            return []
        
        # Filter sentences that are suitable for fill-blank questions
        suitable_sentences = [
            s for s in sentences 
            if len(s.split()) >= 6 and len(s.split()) <= 25
        ]
        
        if not suitable_sentences:
            self.log_warning("No suitable sentences found for fill-blank generation")
            return []
        
        questions = []
        used_sentences = set()
        
        for _ in range(num_questions * 3):  # Try more attempts
            if len(questions) >= num_questions:
                break
                
            # Select a sentence that hasn't been used
            available_sentences = [s for s in suitable_sentences if s not in used_sentences]
            if not available_sentences:
                break
                
            sentence = random.choice(available_sentences)
            
            # Generate question from sentence
            question_data = self._generate_fill_blank_question(sentence)
            
            if question_data:
                questions.append(question_data)
                used_sentences.add(sentence)
        
        return questions[:num_questions]
    
    def _generate_fill_blank_question(self, sentence: str) -> Optional[Question]:
        """
        Generate a fill-in-the-blank question from a sentence.
        
        Args:
            sentence: Source sentence
            
        Returns:
            Generated question or None
        """
        sentence = sentence.strip()
        if not sentence:
            return None
        
        # Tokenize the sentence
        words = sentence.split()
        if len(words) < 6:
            return None
        
        # Find good candidates for blanks
        candidates = self._find_blank_candidates(sentence, words)
        
        if not candidates:
            return None
        
        # Select the best candidate
        blank_info = self._select_best_candidate(candidates, words)
        if not blank_info:
            return None
        
        word_to_blank, word_index, blank_type = blank_info
        
        # Create the question with blank
        question_words = words.copy()
        question_words[word_index] = "_____"
        question_text = " ".join(question_words)
        
        # Ensure proper punctuation
        if not question_text.endswith(('.', '!', '?')):
            question_text += "."
        
        # Determine difficulty based on word characteristics
        difficulty = self._determine_difficulty(word_to_blank, blank_type)
        
        # Calculate confidence based on various factors
        confidence = self._calculate_confidence(word_to_blank, sentence, blank_type)
        
        return Question(
            question=question_text,
            answer=word_to_blank,
            type='fill_blank',
            difficulty=difficulty,
            confidence=confidence,
            source_sentence=sentence,
            metadata={
                'blank_type': blank_type,
                'word_index': word_index,
                'original_word': word_to_blank,
                'context_words': self._get_context_words(words, word_index)
            }
        )
    
    def _find_blank_candidates(self, sentence: str, words: List[str]) -> List[Tuple[str, int, str]]:
        """
        Find good candidates for creating blanks.
        
        Args:
            sentence: Original sentence
            words: List of words in the sentence
            
        Returns:
            List of (word, index, type) tuples
        """
        candidates = []
        
        for i, word in enumerate(words):
            # Clean the word (remove punctuation)
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            # Skip if word is too short or in avoid list
            if len(clean_word) < 3 or clean_word in self.avoid_words:
                continue
            
            # Skip if word is a number
            if clean_word.isdigit():
                continue
            
            # Determine word type and importance
            word_type = self._classify_word(word, sentence)
            importance = self._calculate_word_importance(word, sentence)
            
            if importance > 0.3:  # Only consider important words
                candidates.append((word, i, word_type))
        
        return candidates
    
    def _classify_word(self, word: str, sentence: str) -> str:
        """
        Classify word type for blank generation.
        
        Args:
            word: Word to classify
            sentence: Context sentence
            
        Returns:
            Word classification
        """
        clean_word = re.sub(r'[^\w]', '', word.lower())
        
        # Simple heuristic classification
        if word[0].isupper() and word not in sentence.split()[0]:
            return 'proper_noun'
        elif clean_word.endswith(('ing', 'ed', 'er', 'est')):
            return 'verb_or_adjective'
        elif clean_word.endswith(('tion', 'sion', 'ness', 'ment', 'ity')):
            return 'noun'
        elif clean_word.endswith(('ly')):
            return 'adverb'
        elif len(clean_word) > 6:
            return 'complex_word'
        else:
            return 'general'
    
    def _calculate_word_importance(self, word: str, sentence: str) -> float:
        """
        Calculate the importance of a word for creating a blank.
        
        Args:
            word: Word to evaluate
            sentence: Context sentence
            
        Returns:
            Importance score (0-1)
        """
        clean_word = re.sub(r'[^\w]', '', word.lower())
        importance = 0.0
        
        # Length factor (longer words are often more important)
        if len(clean_word) >= 5:
            importance += 0.3
        elif len(clean_word) >= 3:
            importance += 0.1
        
        # Capitalization (proper nouns are important)
        if word[0].isupper() and word not in sentence.split()[0]:
            importance += 0.4
        
        # Word endings (technical/academic words)
        if clean_word.endswith(('tion', 'sion', 'ness', 'ment', 'ity', 'ism')):
            importance += 0.3
        
        # Avoid very common words
        common_words = {
            'time', 'people', 'way', 'day', 'man', 'thing', 'woman', 'life',
            'child', 'world', 'school', 'state', 'family', 'student', 'group',
            'country', 'problem', 'hand', 'part', 'place', 'case', 'week',
            'company', 'system', 'program', 'question', 'work', 'government'
        }
        
        if clean_word in common_words:
            importance -= 0.2
        
        return max(0.0, min(1.0, importance))
    
    def _select_best_candidate(self, candidates: List[Tuple[str, int, str]], 
                             words: List[str]) -> Optional[Tuple[str, int, str]]:
        """
        Select the best candidate for creating a blank.
        
        Args:
            candidates: List of candidate words
            words: All words in the sentence
            
        Returns:
            Best candidate or None
        """
        if not candidates:
            return None
        
        # Score each candidate
        scored_candidates = []
        for word, index, word_type in candidates:
            score = self._score_candidate(word, index, word_type, words)
            scored_candidates.append((score, word, index, word_type))
        
        # Sort by score and return the best
        scored_candidates.sort(reverse=True)
        best_score, best_word, best_index, best_type = scored_candidates[0]
        
        if best_score > 0.4:  # Minimum threshold
            return (best_word, best_index, best_type)
        
        return None
    
    def _score_candidate(self, word: str, index: int, word_type: str, words: List[str]) -> float:
        """
        Score a candidate word for blank creation.
        
        Args:
            word: Candidate word
            index: Word position in sentence
            word_type: Classification of the word
            words: All words in sentence
            
        Returns:
            Candidate score
        """
        score = 0.0
        clean_word = re.sub(r'[^\w]', '', word.lower())
        
        # Base score from word importance
        score += self._calculate_word_importance(word, " ".join(words))
        
        # Position bonus (avoid first and last words)
        if 1 < index < len(words) - 1:
            score += 0.2
        
        # Word type bonuses
        type_bonuses = {
            'proper_noun': 0.4,
            'noun': 0.3,
            'verb_or_adjective': 0.2,
            'complex_word': 0.3,
            'adverb': 0.1,
            'general': 0.0
        }
        score += type_bonuses.get(word_type, 0.0)
        
        # Length bonus
        if 5 <= len(clean_word) <= 10:
            score += 0.2
        elif len(clean_word) > 10:
            score += 0.1
        
        return score
    
    def _determine_difficulty(self, word: str, word_type: str) -> str:
        """
        Determine question difficulty based on the blank word.
        
        Args:
            word: Word that will be blanked
            word_type: Type of the word
            
        Returns:
            Difficulty level
        """
        clean_word = re.sub(r'[^\w]', '', word.lower())
        
        # Very long or technical words are hard
        if len(clean_word) > 10 or word_type == 'complex_word':
            return 'hard'
        
        # Proper nouns and specialized terms are medium
        if word_type in ['proper_noun', 'noun'] and len(clean_word) > 6:
            return 'medium'
        
        # Short common words are easy
        if len(clean_word) <= 5:
            return 'easy'
        
        return 'medium'
    
    def _calculate_confidence(self, word: str, sentence: str, word_type: str) -> float:
        """
        Calculate confidence score for the question.
        
        Args:
            word: Word that will be blanked
            sentence: Original sentence
            word_type: Type of the word
            
        Returns:
            Confidence score (0-1)
        """
        confidence = 0.5  # Base confidence
        
        # Higher confidence for proper nouns and specific terms
        if word_type == 'proper_noun':
            confidence += 0.3
        elif word_type in ['noun', 'complex_word']:
            confidence += 0.2
        
        # Context clarity bonus
        if len(sentence.split()) >= 10:
            confidence += 0.1
        
        # Word uniqueness in sentence
        word_count = sentence.lower().count(word.lower())
        if word_count == 1:
            confidence += 0.1
        
        return max(0.3, min(0.9, confidence))
    
    def _get_context_words(self, words: List[str], blank_index: int, 
                          context_size: int = 2) -> Dict[str, List[str]]:
        """
        Get context words around the blank.
        
        Args:
            words: All words in sentence
            blank_index: Index of the blank word
            context_size: Number of words to include on each side
            
        Returns:
            Dictionary with before and after context words
        """
        start_idx = max(0, blank_index - context_size)
        end_idx = min(len(words), blank_index + context_size + 1)
        
        before_words = words[start_idx:blank_index]
        after_words = words[blank_index + 1:end_idx]
        
        return {
            'before': before_words,
            'after': after_words
        }
