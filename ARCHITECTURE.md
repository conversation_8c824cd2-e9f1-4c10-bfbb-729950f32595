# QuizAIGen Library Architecture

## Overview
QuizAIGen is a modular, extensible Python library for AI-powered question generation. The architecture follows a layered approach with clear separation of concerns, making it easy to maintain, test, and integrate into external applications.

## High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    QuizAIGen Library                        │
├─────────────────────────────────────────────────────────────┤
│                     Public API Layer                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐  │
│  │ Question        │ │ Batch           │ │ Export       │  │
│  │ Generator API   │ │ Processing API  │ │ Utilities    │  │
│  └─────────────────┘ └─────────────────┘ └──────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  Question Generation Layer                  │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌──────┐  │
│  │   MCQ   │ │ Boolean │ │  Short  │ │  Fill   │ │ Para │  │
│  │Generator│ │Generator│ │ Answer  │ │ Blank   │ │phrase│  │
│  │         │ │         │ │Generator│ │Generator│ │  QA  │  │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └──────┘  │
├─────────────────────────────────────────────────────────────┤
│                   AI Model Orchestration                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐  │
│  │ Model Manager   │ │ Inference       │ │ Model Cache  │  │
│  │ (T5, BERT, GPT) │ │ Pipeline        │ │ & Optimizer  │  │
│  └─────────────────┘ └─────────────────┘ └──────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  Processing & Utilities                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐  │
│  │ Text            │ │ Post-processing │ │ Quality      │  │
│  │ Preprocessing   │ │ & Filtering     │ │ Assessment   │  │
│  │ (spaCy, NLTK)   │ │                 │ │              │  │
│  └─────────────────┘ └─────────────────┘ └──────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     Input Processing                       │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌──────┐  │
│  │  Text   │ │   PDF   │ │  Word   │ │   URL   │ │Multi │  │
│  │Processor│ │Processor│ │Processor│ │Processor│ │media │  │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └──────┘  │
├─────────────────────────────────────────────────────────────┤
│                    Foundation Layer                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌──────────────┐  │
│  │ Configuration   │ │ Logging &       │ │ Exception    │  │
│  │ Management      │ │ Error Handling  │ │ Handling     │  │
│  └─────────────────┘ └─────────────────┘ └──────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Core Modules

### 1. Public API Layer (`quizaigen.api`)
- **QuestionGenerator**: Main entry point for question generation
- **BatchProcessor**: Handles bulk question generation
- **ExportManager**: Manages export to various formats

### 2. Question Generation Layer (`quizaigen.generators`)
- **MCQGenerator**: Multiple choice questions with distractor generation
- **BooleanGenerator**: Yes/No questions
- **ShortAnswerGenerator**: FAQ-style questions
- **FillBlankGenerator**: Fill-in-the-blank questions
- **ParaphraseGenerator**: Question paraphrasing
- **QAGenerator**: Question answering (extractive and boolean)

### 3. AI Model Layer (`quizaigen.models`)
- **ModelManager**: Loads and manages transformer models
- **InferencePipeline**: Orchestrates model inference
- **ModelCache**: Caches models for performance

### 4. Processing Layer (`quizaigen.processing`)
- **TextPreprocessor**: Text cleaning and preparation
- **PostProcessor**: Question filtering and quality control
- **QualityAssessor**: Evaluates question quality and difficulty

### 5. Input Processing (`quizaigen.inputs`)
- **TextProcessor**: Plain text handling
- **DocumentProcessor**: PDF/Word document processing
- **URLProcessor**: Web content extraction
- **MultimediaProcessor**: Transcript processing

### 6. Foundation Layer (`quizaigen.core`)
- **Config**: Configuration management
- **Logger**: Logging utilities
- **Exceptions**: Custom exception classes

## Key Design Principles

### 1. Modularity
- Each component has a single responsibility
- Clear interfaces between modules
- Easy to test and maintain individual components

### 2. Extensibility
- Plugin architecture for new question types
- Configurable model backends
- Extensible export formats

### 3. Performance
- Lazy loading of models
- Efficient batch processing
- Caching mechanisms

### 4. Integration-Ready
- Clean, documented APIs
- No SaaS-specific dependencies
- Flexible configuration options

## Data Flow

1. **Input Processing**: Raw content → Preprocessed text
2. **Model Inference**: Preprocessed text → Raw questions/answers
3. **Post-processing**: Raw output → Filtered, quality-checked questions
4. **Export**: Processed questions → Various output formats

## Configuration System

```python
# Example configuration structure
{
    "models": {
        "mcq": {"name": "t5-base", "cache": True},
        "boolean": {"name": "bert-base", "cache": True}
    },
    "processing": {
        "max_questions": 50,
        "min_quality_score": 0.7,
        "remove_duplicates": True
    },
    "export": {
        "default_format": "json",
        "include_metadata": True
    }
}
```

## Extension Points

1. **Custom Question Types**: Implement `BaseQuestionGenerator`
2. **Custom Models**: Implement `BaseModel` interface
3. **Custom Processors**: Implement `BaseProcessor` interface
4. **Custom Exporters**: Implement `BaseExporter` interface

This architecture ensures the library is robust, maintainable, and ready for future enhancements while maintaining clean separation between core functionality and integration concerns.
