"""
Batch Processor

This module handles batch processing of multiple inputs for question generation.
"""

import time
import logging
from typing import List, Dict, Any, Optional, Union, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path

from ..generators import (
    MCQGenerator, BooleanGenerator, FAQGenerator, 
    FillBlankGenerator, QuestionParaphraser, QuestionAnswerer
)
from ..inputs import TextProcessor, DocumentProcessor, URLProcessor
from ..core.exceptions import ProcessingError, ValidationError


class BatchProcessor:
    """Handles batch processing of multiple inputs for question generation."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize BatchProcessor.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # Initialize processors
        self.text_processor = TextProcessor(config)
        self.document_processor = DocumentProcessor(config)
        self.url_processor = URLProcessor(config)
        
        # Initialize generators
        self.generators = {
            'mcq': MCQGenerator(config),
            'boolean': <PERSON>oleanGenerator(config),
            'faq': FAQGenerator(config),
            'fill_blank': FillBlankGenerator(config),
        }
        
        # Initialize other components
        self.paraphraser = QuestionParaphraser(config)
        self.answerer = QuestionAnswerer(config)
        
        # Batch processing settings
        self.max_workers = self.config.get('max_workers', 4)
        self.timeout_per_item = self.config.get('timeout_per_item', 300)  # 5 minutes
        self.continue_on_error = self.config.get('continue_on_error', True)
        
        self.logger.info("Initialized BatchProcessor")
    
    def process_batch(self, inputs: List[Dict[str, Any]], 
                     question_types: List[str] = None,
                     num_questions_per_type: int = 5,
                     **kwargs) -> Dict[str, Any]:
        """
        Process a batch of inputs to generate questions.
        
        Args:
            inputs: List of input dictionaries with 'type' and 'content' keys
            question_types: List of question types to generate
            num_questions_per_type: Number of questions per type
            **kwargs: Additional processing options
        
        Returns:
            Dictionary with batch processing results
        """
        if not inputs:
            raise ValueError("No inputs provided for batch processing")
        
        question_types = question_types or ['mcq', 'boolean', 'faq']
        
        self.logger.info(f"Starting batch processing of {len(inputs)} inputs")
        
        start_time = time.time()
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [],
            'errors': [],
            'processing_time': 0,
            'summary': {}
        }
        
        # Process inputs
        if self.max_workers > 1:
            results = self._process_batch_parallel(
                inputs, question_types, num_questions_per_type, **kwargs
            )
        else:
            results = self._process_batch_sequential(
                inputs, question_types, num_questions_per_type, **kwargs
            )
        
        # Calculate final metrics
        results['processing_time'] = time.time() - start_time
        results['summary'] = self._generate_summary(results)
        
        self.logger.info(f"Batch processing completed in {results['processing_time']:.2f} seconds")
        
        return results
    
    def _process_batch_sequential(self, inputs: List[Dict[str, Any]], 
                                question_types: List[str],
                                num_questions_per_type: int,
                                **kwargs) -> Dict[str, Any]:
        """Process inputs sequentially."""
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [],
            'errors': []
        }
        
        for i, input_data in enumerate(inputs):
            try:
                self.logger.info(f"Processing input {i+1}/{len(inputs)}")
                
                result = self._process_single_input(
                    input_data, question_types, num_questions_per_type, **kwargs
                )
                
                if result['success']:
                    results['inputs_processed'] += 1
                    results['total_questions_generated'] += result['total_questions']
                else:
                    results['inputs_failed'] += 1
                    results['errors'].append({
                        'input_index': i,
                        'error': result.get('error', 'Unknown error')
                    })
                
                results['results'].append(result)
            
            except Exception as e:
                self.logger.error(f"Error processing input {i}: {str(e)}")
                results['inputs_failed'] += 1
                results['errors'].append({
                    'input_index': i,
                    'error': str(e)
                })
                
                if not self.continue_on_error:
                    break
        
        return results
    
    def _process_batch_parallel(self, inputs: List[Dict[str, Any]], 
                              question_types: List[str],
                              num_questions_per_type: int,
                              **kwargs) -> Dict[str, Any]:
        """Process inputs in parallel."""
        results = {
            'inputs_processed': 0,
            'inputs_failed': 0,
            'total_questions_generated': 0,
            'results': [None] * len(inputs),
            'errors': []
        }
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_index = {
                executor.submit(
                    self._process_single_input,
                    input_data, question_types, num_questions_per_type, **kwargs
                ): i
                for i, input_data in enumerate(inputs)
            }
            
            # Collect results
            for future in as_completed(future_to_index, timeout=self.timeout_per_item * len(inputs)):
                index = future_to_index[future]
                
                try:
                    result = future.result()
                    
                    if result['success']:
                        results['inputs_processed'] += 1
                        results['total_questions_generated'] += result['total_questions']
                    else:
                        results['inputs_failed'] += 1
                        results['errors'].append({
                            'input_index': index,
                            'error': result.get('error', 'Unknown error')
                        })
                    
                    results['results'][index] = result
                
                except Exception as e:
                    self.logger.error(f"Error processing input {index}: {str(e)}")
                    results['inputs_failed'] += 1
                    results['errors'].append({
                        'input_index': index,
                        'error': str(e)
                    })
                    
                    results['results'][index] = {
                        'success': False,
                        'error': str(e),
                        'input_data': inputs[index]
                    }
        
        return results
    
    def _process_single_input(self, input_data: Dict[str, Any], 
                            question_types: List[str],
                            num_questions_per_type: int,
                            **kwargs) -> Dict[str, Any]:
        """
        Process a single input to generate questions.
        
        Args:
            input_data: Input data dictionary
            question_types: List of question types to generate
            num_questions_per_type: Number of questions per type
            **kwargs: Additional options
        
        Returns:
            Processing result dictionary
        """
        try:
            # Extract text from input
            text = self._extract_text_from_input(input_data)
            
            if not text or len(text.strip()) < 50:
                return {
                    'success': False,
                    'error': 'Insufficient text content',
                    'input_data': input_data
                }
            
            # Generate questions for each type
            all_questions = {}
            total_questions = 0
            
            for question_type in question_types:
                if question_type not in self.generators:
                    self.logger.warning(f"Unknown question type: {question_type}")
                    continue
                
                try:
                    generator = self.generators[question_type]
                    questions = generator.generate_questions(
                        text, num_questions_per_type, **kwargs
                    )
                    
                    all_questions[question_type] = [q.to_dict() for q in questions]
                    total_questions += len(questions)
                
                except Exception as e:
                    self.logger.error(f"Error generating {question_type} questions: {str(e)}")
                    all_questions[question_type] = []
            
            return {
                'success': True,
                'input_data': input_data,
                'text_length': len(text),
                'questions': all_questions,
                'total_questions': total_questions,
                'question_types_generated': list(all_questions.keys())
            }
        
        except Exception as e:
            self.logger.error(f"Error processing input: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'input_data': input_data
            }
    
    def _extract_text_from_input(self, input_data: Dict[str, Any]) -> str:
        """
        Extract text from various input types.
        
        Args:
            input_data: Input data dictionary
        
        Returns:
            Extracted text
        """
        input_type = input_data.get('type', '').lower()
        content = input_data.get('content', '')
        
        if input_type == 'text':
            return content
        
        elif input_type == 'file':
            file_path = content
            if not Path(file_path).exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            result = self.document_processor.process(file_path)
            if not result['success']:
                raise ProcessingError(f"Failed to process file: {result.get('error', 'Unknown error')}")
            
            return result['text']
        
        elif input_type == 'url':
            url = content
            result = self.url_processor.process(url)
            if not result['success']:
                raise ProcessingError(f"Failed to process URL: {result.get('error', 'Unknown error')}")
            
            return result['text']
        
        else:
            raise ValueError(f"Unsupported input type: {input_type}")
    
    def _generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of batch processing results.
        
        Args:
            results: Batch processing results
        
        Returns:
            Summary dictionary
        """
        total_inputs = results['inputs_processed'] + results['inputs_failed']
        success_rate = results['inputs_processed'] / total_inputs if total_inputs > 0 else 0
        
        # Count questions by type
        question_counts = {}
        for result in results['results']:
            if result and result.get('success') and 'questions' in result:
                for q_type, questions in result['questions'].items():
                    question_counts[q_type] = question_counts.get(q_type, 0) + len(questions)
        
        return {
            'total_inputs': total_inputs,
            'success_rate': success_rate,
            'average_questions_per_input': (
                results['total_questions_generated'] / results['inputs_processed']
                if results['inputs_processed'] > 0 else 0
            ),
            'questions_by_type': question_counts,
            'processing_time_per_input': (
                results['processing_time'] / total_inputs
                if total_inputs > 0 else 0
            )
        }
    
    def process_files(self, file_paths: List[str], **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process multiple files.
        
        Args:
            file_paths: List of file paths
            **kwargs: Additional processing options
        
        Returns:
            Batch processing results
        """
        inputs = [{'type': 'file', 'content': path} for path in file_paths]
        return self.process_batch(inputs, **kwargs)
    
    def process_urls(self, urls: List[str], **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process multiple URLs.
        
        Args:
            urls: List of URLs
            **kwargs: Additional processing options
        
        Returns:
            Batch processing results
        """
        inputs = [{'type': 'url', 'content': url} for url in urls]
        return self.process_batch(inputs, **kwargs)
    
    def process_texts(self, texts: List[str], **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process multiple text inputs.
        
        Args:
            texts: List of text strings
            **kwargs: Additional processing options
        
        Returns:
            Batch processing results
        """
        inputs = [{'type': 'text', 'content': text} for text in texts]
        return self.process_batch(inputs, **kwargs)
