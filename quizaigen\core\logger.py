"""
QuizAIGen Logging Utilities

This module provides logging functionality for the QuizAIGen library.
"""

import logging
import sys
from typing import Optional
from pathlib import Path


def get_logger(
    name: str = "quizaigen",
    level: str = "INFO",
    log_file: Optional[str] = None,
    format_string: Optional[str] = None
) -> logging.Logger:
    """
    Get a configured logger for QuizAIGen.
    
    Args:
        name: Logger name
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional log file path
        format_string: Optional custom format string
    
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid adding multiple handlers if logger already exists
    if logger.handlers:
        return logger
    
    # Set logging level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Default format
    if format_string is None:
        format_string = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "%(filename)s:%(lineno)d - %(message)s"
        )
    
    formatter = logging.Formatter(format_string)
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (optional)
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    disable_existing_loggers: bool = False
) -> None:
    """
    Setup logging configuration for the entire QuizAIGen library.
    
    Args:
        level: Logging level
        log_file: Optional log file path
        disable_existing_loggers: Whether to disable existing loggers
    """
    logging_config = {
        'version': 1,
        'disable_existing_loggers': disable_existing_loggers,
        'formatters': {
            'standard': {
                'format': (
                    '%(asctime)s - %(name)s - %(levelname)s - '
                    '%(filename)s:%(lineno)d - %(message)s'
                )
            },
            'detailed': {
                'format': (
                    '%(asctime)s - %(name)s - %(levelname)s - '
                    '%(module)s - %(funcName)s:%(lineno)d - %(message)s'
                )
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': level,
                'formatter': 'standard',
                'stream': 'ext://sys.stdout'
            }
        },
        'loggers': {
            'quizaigen': {
                'level': level,
                'handlers': ['console'],
                'propagate': False
            }
        },
        'root': {
            'level': level,
            'handlers': ['console']
        }
    }
    
    # Add file handler if log_file is specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logging_config['handlers']['file'] = {
            'class': 'logging.FileHandler',
            'level': level,
            'formatter': 'detailed',
            'filename': str(log_path),
            'encoding': 'utf-8'
        }
        
        logging_config['loggers']['quizaigen']['handlers'].append('file')
        logging_config['root']['handlers'].append('file')
    
    logging.config.dictConfig(logging_config)


class LoggerMixin:
    """Mixin class to add logging functionality to other classes."""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for this class."""
        if not hasattr(self, '_logger'):
            self._logger = get_logger(f"quizaigen.{self.__class__.__name__}")
        return self._logger
    
    def log_debug(self, message: str, **kwargs) -> None:
        """Log debug message."""
        self.logger.debug(message, **kwargs)
    
    def log_info(self, message: str, **kwargs) -> None:
        """Log info message."""
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs) -> None:
        """Log warning message."""
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs) -> None:
        """Log error message."""
        self.logger.error(message, **kwargs)
    
    def log_critical(self, message: str, **kwargs) -> None:
        """Log critical message."""
        self.logger.critical(message, **kwargs)


# Import logging.config for dictConfig
import logging.config
