../../Scripts/nltk.exe,sha256=8SE_NJdHLriaG3mwUtXP_jlK6nOVG1K98ApT7erSqqs,108402
nltk-3.9.1.dist-info/AUTHORS.md,sha256=8TBB-aK5lRpYuioJLnMnFStChn0YSei7jTa2Y9cveCQ,7622
nltk-3.9.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
nltk-3.9.1.dist-info/LICENSE.txt,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
nltk-3.9.1.dist-info/METADATA,sha256=FKI5gQ1QY3d-52flb9kwnFWRheF6tMVz2eNWPOcn4C8,2886
nltk-3.9.1.dist-info/README.md,sha256=wXHHwPJ7LQ3u_v7IbAXj8aeo0dQCLySMbqcgMAbMPbs,1740
nltk-3.9.1.dist-info/RECORD,,
nltk-3.9.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk-3.9.1.dist-info/WHEEL,sha256=HiCZjzuy6Dw0hdX5R3LCFPDmFS4BWl8H-8W39XfmgX4,91
nltk-3.9.1.dist-info/entry_points.txt,sha256=hw9pIVCowwI3Desu2qq4w07Aba7cGJnxwY4DZho1vO4,38
nltk-3.9.1.dist-info/top_level.txt,sha256=YoQ-mwqckmTv1Qktmlk5Ylb6lDG77jg5qwoEB7c-pXo,5
nltk/VERSION,sha256=tDuBAibJYl_bDXqtkcAZqEI7QMSj16gFYsNyXuffeRk,6
nltk/__init__.py,sha256=9M6SialNa1fDQeziRTDwNn2JvG-8BybOeQYPTirGWzg,6225
nltk/__pycache__/__init__.cpython-312.pyc,,
nltk/__pycache__/book.cpython-312.pyc,,
nltk/__pycache__/cli.cpython-312.pyc,,
nltk/__pycache__/collections.cpython-312.pyc,,
nltk/__pycache__/collocations.cpython-312.pyc,,
nltk/__pycache__/compat.cpython-312.pyc,,
nltk/__pycache__/data.cpython-312.pyc,,
nltk/__pycache__/decorators.cpython-312.pyc,,
nltk/__pycache__/downloader.cpython-312.pyc,,
nltk/__pycache__/featstruct.cpython-312.pyc,,
nltk/__pycache__/grammar.cpython-312.pyc,,
nltk/__pycache__/help.cpython-312.pyc,,
nltk/__pycache__/internals.cpython-312.pyc,,
nltk/__pycache__/jsontags.cpython-312.pyc,,
nltk/__pycache__/langnames.cpython-312.pyc,,
nltk/__pycache__/lazyimport.cpython-312.pyc,,
nltk/__pycache__/probability.cpython-312.pyc,,
nltk/__pycache__/tabdata.cpython-312.pyc,,
nltk/__pycache__/text.cpython-312.pyc,,
nltk/__pycache__/tgrep.cpython-312.pyc,,
nltk/__pycache__/toolbox.cpython-312.pyc,,
nltk/__pycache__/treeprettyprinter.cpython-312.pyc,,
nltk/__pycache__/treetransforms.cpython-312.pyc,,
nltk/__pycache__/util.cpython-312.pyc,,
nltk/__pycache__/wsd.cpython-312.pyc,,
nltk/app/__init__.py,sha256=b3V4KvB27KR_ZlREwxs5q29OOWkRFNthhzBcb6qjbLs,1531
nltk/app/__pycache__/__init__.cpython-312.pyc,,
nltk/app/__pycache__/chartparser_app.cpython-312.pyc,,
nltk/app/__pycache__/chunkparser_app.cpython-312.pyc,,
nltk/app/__pycache__/collocations_app.cpython-312.pyc,,
nltk/app/__pycache__/concordance_app.cpython-312.pyc,,
nltk/app/__pycache__/nemo_app.cpython-312.pyc,,
nltk/app/__pycache__/rdparser_app.cpython-312.pyc,,
nltk/app/__pycache__/srparser_app.cpython-312.pyc,,
nltk/app/__pycache__/wordfreq_app.cpython-312.pyc,,
nltk/app/__pycache__/wordnet_app.cpython-312.pyc,,
nltk/app/chartparser_app.py,sha256=L608WxpvIIPxt5ReozJxNJemnPQ8pUwMJgQE5yNRCA0,85626
nltk/app/chunkparser_app.py,sha256=HqHvbEHQkWGXisNnCC6y4MLqYuLHC1jWqvvEwQDNq6k,56813
nltk/app/collocations_app.py,sha256=nYwfDKjcfyvSsCA4seQCOamo6QYHGLSXHvbmrlCEheE,14226
nltk/app/concordance_app.py,sha256=FGMMTPjzokzS1nqFaF6AcbXz6bT_1vpFe02fMIJMzS4,24173
nltk/app/nemo_app.py,sha256=YPh-6P9gkM8hBgslPkKPzjOa1CHInKWzIFoKQl5ma_0,12142
nltk/app/rdparser_app.py,sha256=5-1bWcdSNlofmLp4RZJOPeuph-V5Oy2v7_LqeK6HouU,36729
nltk/app/srparser_app.py,sha256=t8QtRgB0pmMAaIgC4vyWdY0cZP4dy7t7FgqaebOGEtM,33464
nltk/app/wordfreq_app.py,sha256=lT9Mr3gtN9VIXIKIUz6WXiztO_I9wIxaVt7UXAbvoOY,921
nltk/app/wordnet_app.py,sha256=GYmuoaPNz6cTchvqBSy6tzNTMuhWJAOP_GAFIi6KZPA,34568
nltk/book.py,sha256=Ldlen3xNvqNBKofTH2KIVKI4q9cgK1SWBRydEiGxwAE,3699
nltk/ccg/__init__.py,sha256=KBOPTnvQqOt0nngxyzAyZzx8Gzqz0RyV4_hNM6nBAL0,881
nltk/ccg/__pycache__/__init__.cpython-312.pyc,,
nltk/ccg/__pycache__/api.cpython-312.pyc,,
nltk/ccg/__pycache__/chart.cpython-312.pyc,,
nltk/ccg/__pycache__/combinator.cpython-312.pyc,,
nltk/ccg/__pycache__/lexicon.cpython-312.pyc,,
nltk/ccg/__pycache__/logic.cpython-312.pyc,,
nltk/ccg/api.py,sha256=H6vz9cAxUZu8aTlUtdbj5z8xYF1JCqoPRubzM2nAe7M,9998
nltk/ccg/chart.py,sha256=CTr6gW5yh5j6P-i0ZS69nU8Rz3sWWiU1a_Bs3EIGv1I,13665
nltk/ccg/combinator.py,sha256=MBQBGWQ0ySMjWqS-tFDQD-5ivCKlkYuHlMk9HXEZEBc,10295
nltk/ccg/lexicon.py,sha256=KyuIdOwwBtwKf6XzBMTKLbGXY4QAyy2oMLYz68Ps8rk,9525
nltk/ccg/logic.py,sha256=viXvlilQAI6XY1kr64xUZREIVnasKHcblwv6_sSYY5U,1811
nltk/chat/__init__.py,sha256=pNFiAfrm3ZhbbT0hA2jTJANTaR0Z3IYSMYuEyQxAd1A,1508
nltk/chat/__pycache__/__init__.cpython-312.pyc,,
nltk/chat/__pycache__/eliza.cpython-312.pyc,,
nltk/chat/__pycache__/iesha.cpython-312.pyc,,
nltk/chat/__pycache__/rude.cpython-312.pyc,,
nltk/chat/__pycache__/suntsu.cpython-312.pyc,,
nltk/chat/__pycache__/util.cpython-312.pyc,,
nltk/chat/__pycache__/zen.cpython-312.pyc,,
nltk/chat/eliza.py,sha256=XuPW0DuAcVELTOTjuxzHQq1-t4kFEEOJDquG3EPKzuc,9295
nltk/chat/iesha.py,sha256=Ge6MmpRv9q9YyRdsQkOVKPDRD8mLeSzXTpa-hJW6tww,4247
nltk/chat/rude.py,sha256=lZRz-ZiMS-BZR15RJ0j_5yJM7qoNQEK1-NxPQNMYsjA,3164
nltk/chat/suntsu.py,sha256=rAvsjSaiHZIsaNDgag1aZO49CCdCdV_hJ4zk6WLqshc,7045
nltk/chat/util.py,sha256=EnUQ_y2lepT0nwt99cbD_n_70-WNQMxuNfpdw5UpLb4,3888
nltk/chat/zen.py,sha256=BrJ7kS7H6DS8N4iuF5C98tN6hDrX1FkTvTOVt3qP8Ic,11350
nltk/chunk/__init__.py,sha256=dL2ylEzSQ9u4pZkZ3YDqbrnxMbW1tsKaf8yyHQ5QDVg,7635
nltk/chunk/__pycache__/__init__.cpython-312.pyc,,
nltk/chunk/__pycache__/api.cpython-312.pyc,,
nltk/chunk/__pycache__/named_entity.cpython-312.pyc,,
nltk/chunk/__pycache__/regexp.cpython-312.pyc,,
nltk/chunk/__pycache__/util.cpython-312.pyc,,
nltk/chunk/api.py,sha256=lgenXy_vYxW6UThtznT4W-lV4_XUErEvIHKuerhjKRs,1890
nltk/chunk/named_entity.py,sha256=7cM9Gg_zlfT2dIjRiGCnr_G8pp9Kdw0AQMNjNOrEmZU,12414
nltk/chunk/regexp.py,sha256=WcTkiYZvrQbZNDp2YDUJKmrplrZw4N-aGDBbWJimBQk,54504
nltk/chunk/util.py,sha256=dSojC2KaUdAdGwTjqWN5ngcAyYDftusZGARamLnukOE,20665
nltk/classify/__init__.py,sha256=4pL0sRHoks7cYKZae2LKPtgPKJflH2QuynlgDz16j1g,4495
nltk/classify/__pycache__/__init__.cpython-312.pyc,,
nltk/classify/__pycache__/api.cpython-312.pyc,,
nltk/classify/__pycache__/decisiontree.cpython-312.pyc,,
nltk/classify/__pycache__/maxent.cpython-312.pyc,,
nltk/classify/__pycache__/megam.cpython-312.pyc,,
nltk/classify/__pycache__/naivebayes.cpython-312.pyc,,
nltk/classify/__pycache__/positivenaivebayes.cpython-312.pyc,,
nltk/classify/__pycache__/rte_classify.cpython-312.pyc,,
nltk/classify/__pycache__/scikitlearn.cpython-312.pyc,,
nltk/classify/__pycache__/senna.cpython-312.pyc,,
nltk/classify/__pycache__/svm.cpython-312.pyc,,
nltk/classify/__pycache__/tadm.cpython-312.pyc,,
nltk/classify/__pycache__/textcat.cpython-312.pyc,,
nltk/classify/__pycache__/util.cpython-312.pyc,,
nltk/classify/__pycache__/weka.cpython-312.pyc,,
nltk/classify/api.py,sha256=BuR_l9T0HkeJVMNuj0eDCwzPlrzfxKfITm_7YdxtRMQ,6430
nltk/classify/decisiontree.py,sha256=Bz8UGUeD7ia6VHpQdL65EdtsKFqtu5A-S3thkdYHKi0,12732
nltk/classify/maxent.py,sha256=5sP36egcqxczQ4LKOzARbfxZwoQWYGsSsa4c-zE7xsI,61103
nltk/classify/megam.py,sha256=g-HXsSTsMnCmRxaS_X-6Ki516Oxh1AGRzqEATpnAq4Q,6210
nltk/classify/naivebayes.py,sha256=YLZgPfq7CHO03U6yf9zGMu_yYwkW2jzFms-4lpmzvSY,10441
nltk/classify/positivenaivebayes.py,sha256=Dj6cSbkpSmmoBWfkaK_G2WjEqGDeVWvqIl_K6KBGVyM,7232
nltk/classify/rte_classify.py,sha256=GzQnQ1VqR0Ah3juSRWicWQU0j403jkYJ6ZlykRUNNOc,6118
nltk/classify/scikitlearn.py,sha256=nWZAqxD-pMLb0ODR4WadQB4wz27N9nbUr44D3NU-_mg,5405
nltk/classify/senna.py,sha256=9p07zHEwVkZSy5yn6fDir9AYJ5OtTLK8XIYUGanaSIc,6754
nltk/classify/svm.py,sha256=Yu4SB3SzwHPPszGDtE2z_dx3RK4Uj-ivU52JR2xeoUk,508
nltk/classify/tadm.py,sha256=DczEtrlrc6xwegpkblnnlzqpWaM4c7hk3wjAfWQcdTY,3433
nltk/classify/textcat.py,sha256=VlZ3ag-b6o6s26bfPs_bntPkg-wkUFsK6jnOI9b-hQI,5804
nltk/classify/util.py,sha256=8bGpP2Jin2CgTx0SeHKVZHnuHP3JaYC7FDHNwUTLXUA,12112
nltk/classify/weka.py,sha256=ApGE9wVzNzLG6Yq9jZ3iVD4gx1o7WtujlriJLbQ_9Bc,12557
nltk/cli.py,sha256=hHfvmnbPJi87Fx5M9UVTIIG7m9crQw8kbB1Fzy0G8gA,1842
nltk/cluster/__init__.py,sha256=TSV9cBWTA-46I9MY5E3IUEoql-8XZFqOCAlVThvsBhw,4269
nltk/cluster/__pycache__/__init__.cpython-312.pyc,,
nltk/cluster/__pycache__/api.cpython-312.pyc,,
nltk/cluster/__pycache__/em.cpython-312.pyc,,
nltk/cluster/__pycache__/gaac.cpython-312.pyc,,
nltk/cluster/__pycache__/kmeans.cpython-312.pyc,,
nltk/cluster/__pycache__/util.cpython-312.pyc,,
nltk/cluster/api.py,sha256=rvW90Ltc3WI2DqS6K-R6z0aSsHPV1NrWSkHF4x4bV4s,2088
nltk/cluster/em.py,sha256=TPF5UyVvg6RNpuMBLzQAhY3g70A2bsUr4lwKoRUpDn0,8200
nltk/cluster/gaac.py,sha256=CToy9Xj0gFlOI264lM0prxpiY5_xl0oiEX3KChsLR3Y,5751
nltk/cluster/kmeans.py,sha256=KVGZ_lbF6Lv2bw7ToPZJAapmUm5yyZNiZiYpYMxSYsg,8360
nltk/cluster/util.py,sha256=vmzEUpfFvlMQJqSRPIm_8Ss8HlZoM9PKmdzrM3slAZE,9739
nltk/collections.py,sha256=_ugarFxNVTrT3gFWh2E52VBUUrDEEXrSbF-ObSk0eCU,22812
nltk/collocations.py,sha256=ybqTeESmy34GLzxtog_-rEVWYNY6Ka688FrSqXTwlfk,14552
nltk/compat.py,sha256=-Fr2P2YCMkDuKfGsSuT4eZND0JDAuVsHn-KiJ-sOyxg,1123
nltk/corpus/__init__.py,sha256=sTrDfXDPSt7IGTyIf_JKFcOwmYNARtsC9OclRjLrlNo,17048
nltk/corpus/__pycache__/__init__.cpython-312.pyc,,
nltk/corpus/__pycache__/europarl_raw.cpython-312.pyc,,
nltk/corpus/__pycache__/util.cpython-312.pyc,,
nltk/corpus/europarl_raw.py,sha256=DtdDbBAjVIv63ixqSXv-2H0T62SfNOQKZ752vXImmVc,1840
nltk/corpus/reader/__init__.py,sha256=_YcJJIP7nrPiy2a0YZXsV70hMHkOH_S4197gq0Vceuc,6491
nltk/corpus/reader/__pycache__/__init__.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/aligned.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/api.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bcp47.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bnc.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/bracket_parse.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/categorized_sents.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/chasen.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/childes.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/chunked.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/cmudict.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/comparative_sents.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/conll.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/crubadan.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/dependency.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/framenet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ieer.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/indian.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ipipan.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/knbc.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/lin.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/markdown.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/mte.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nkjp.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nombank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/nps_chat.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/opinion_lexicon.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/panlex_lite.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/panlex_swadesh.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/pl196x.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/plaintext.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ppattach.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/propbank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/pros_cons.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/reviews.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/rte.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/semcor.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/senseval.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/sentiwordnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/sinica_treebank.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/string_category.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/switchboard.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/tagged.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/timit.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/toolbox.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/twitter.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/udhr.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/util.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/verbnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/wordlist.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/wordnet.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/xmldocs.cpython-312.pyc,,
nltk/corpus/reader/__pycache__/ycoe.cpython-312.pyc,,
nltk/corpus/reader/aligned.py,sha256=srDX9oyiapK1S6wRpwI3R7T_OaRAz6sZiOydU7ilLg4,4851
nltk/corpus/reader/api.py,sha256=zTCtK-G68S7zgXKu_oaTyDpmGzk_i7WZWgnwp7tlsEo,19154
nltk/corpus/reader/bcp47.py,sha256=kn9wo_gCjACj0elh3VI66pO5vLH6QpEUlCKYqbPfym8,8316
nltk/corpus/reader/bnc.py,sha256=zwDDGMSgpjtf71E4TAmd_LKXIqqy33ChTgEN6rdZqZ8,9451
nltk/corpus/reader/bracket_parse.py,sha256=vHqjOSyZwCKdIb7Muvwdh_njluOnmeLwC7JpASwDzXQ,9382
nltk/corpus/reader/categorized_sents.py,sha256=dWHtJ5NC0a6ANulWz_wKSTPOkumdBvE8uK7uvUb2MNk,6053
nltk/corpus/reader/chasen.py,sha256=dpppfuuHynBMeKrwUH2leeOZGHbpxSmzP68vPNybPp0,4537
nltk/corpus/reader/childes.py,sha256=Cbke8thFYcQV9OeilUCDXkFOEa4Iql3x2Vq6APSZUBY,25467
nltk/corpus/reader/chunked.py,sha256=lJn2jZqZ2O7YqhIaox1RILtdtJiCJPiirZHZu7GPjHA,9093
nltk/corpus/reader/cmudict.py,sha256=H2VxHFmFqYnTK2hGd7Nq0oJ_4tN4PrcvTdjBhOhoN0U,3278
nltk/corpus/reader/comparative_sents.py,sha256=QgjCR8FS1gY7_vqHefl8xSZ_3aoFjKavTV_c3UcIy04,11758
nltk/corpus/reader/conll.py,sha256=-SphhD4GSg-Ri189VkJ4ZUy-2lS0pzhjsGQNMd7UzG0,21718
nltk/corpus/reader/crubadan.py,sha256=7e9EkcnAb7K6msuOPUcdMbxWQOQh_DEK2YExWu5gHZc,3521
nltk/corpus/reader/dependency.py,sha256=iMZB_4aDBsnskSeAg4lsfqQqwI8DM6KNxEMUl7gXv0Y,3775
nltk/corpus/reader/framenet.py,sha256=Mc4o8Tn9NWyHfeZ3KfFa1gDwZFYlro1CvD3wAsV5P5Q,131312
nltk/corpus/reader/ieer.py,sha256=DIObZ1DzvFE-IsjrlqEHQ-tYzrosQhdLxwX6gLFoQs4,3686
nltk/corpus/reader/indian.py,sha256=t14x9qfWTVBglf7dZFwMsP6cM1Y48MOhgVUUCYfaAJ4,2921
nltk/corpus/reader/ipipan.py,sha256=ogAMAoXY8LMbpIUwBtYY0Wr2IuoULU9vRGN9OxYecek,12734
nltk/corpus/reader/knbc.py,sha256=a_RfirR7GUP10p9zw1rWDYjbao9hBNLpDX3I-bM6NfY,5597
nltk/corpus/reader/lin.py,sha256=knPM8wKzCbV7jjZLCK0KxmHo8q542igqh7zJRs2izCQ,6471
nltk/corpus/reader/markdown.py,sha256=wjZ3JxuADIiQV1ljMizFeEVwnPOUCg8aFF7mMdlq-uo,11744
nltk/corpus/reader/mte.py,sha256=kznECxvS01EejmzxkVs4dc2uVhXgl7ggl4XPD0-XGVg,13989
nltk/corpus/reader/nkjp.py,sha256=1hsL6edg-zw1j2A_fFYoBCFKlYjy_5nX4Bwz1VL_Va0,15844
nltk/corpus/reader/nombank.py,sha256=8yKMVoSbVthAMcjfpD605f6T8Ejc8N3gJuhRGgW55TU,15778
nltk/corpus/reader/nps_chat.py,sha256=GZ68wfGMaZHezClMDEyC4qiWfOwKswlHs8YD4ktr0Oo,2850
nltk/corpus/reader/opinion_lexicon.py,sha256=izEaF7UqgTEvkDW-8gW4vSvmmtguHYIQI9jixonhJG0,4105
nltk/corpus/reader/panlex_lite.py,sha256=kU7wMbipYZBPqb8x1VDKhct7tzeg_MT3HLZMFXXSMRg,5266
nltk/corpus/reader/panlex_swadesh.py,sha256=ySWwKyETRzp5X2TBpA8tfrHKurjtJLtZYF5JiO-1F2Y,3192
nltk/corpus/reader/pl196x.py,sha256=v7bEKUZhXNXNqeTQ0ZbAKH4JmfxiQLVsrJl3qJLrg5U,11943
nltk/corpus/reader/plaintext.py,sha256=HliCDXBYkh2mnsawOf9C38i_oeF-Dhy5f85qGWu_D70,8257
nltk/corpus/reader/ppattach.py,sha256=A6ec3nL5KrCTOk90jDXtaRaokcRM1qrtG-Bg6Um9m3g,2808
nltk/corpus/reader/propbank.py,sha256=s3VTDN3jzQRFuOmFuR702TFfAcfMej4KUe4FHSOPr9U,17253
nltk/corpus/reader/pros_cons.py,sha256=HRFG8F5ex5DJFSgf56ETGeSe6-MZLt26BRvuV7_xDoI,4763
nltk/corpus/reader/reviews.py,sha256=vgTc180mBWbxLjjQZA-T7o2gndzmqEpXezKv5Bm_tGA,11990
nltk/corpus/reader/rte.py,sha256=SDVAuItRtZAIaM76rlT_TowYL4YipwidxG-kzglhwZ4,4639
nltk/corpus/reader/semcor.py,sha256=IPcnQBq9TYxNzU0BcD-iqfgMrMl4VIh_VSj7-zGBuWI,11398
nltk/corpus/reader/senseval.py,sha256=kf5JY3jUoqXX6PDXUyY55LnoQ06i_aPItZcP-iRFLV0,7343
nltk/corpus/reader/sentiwordnet.py,sha256=7PWwSaXM22waSszIlKdZc8KOO3GWABbavmclkhOX1_M,4490
nltk/corpus/reader/sinica_treebank.py,sha256=NNdsw-QHpoWnuX---Q5yglxsxNemvty-UIi3AR6G2EQ,2466
nltk/corpus/reader/string_category.py,sha256=tE_AdI_CG1ktQLnjZlLKJzweewE0LHwO4NeiFrraMyU,1863
nltk/corpus/reader/switchboard.py,sha256=90yaV5B7hYSHr6Vr1NI3lxzLsWwuSVApHzRnJqc9TtA,4422
nltk/corpus/reader/tagged.py,sha256=qZwnAAkFimpfRNBvBotMwnaPlB614lSF3YPt-AE_6EA,11786
nltk/corpus/reader/timit.py,sha256=fGOKYeN_v6PSbY376GM_0o_13cZA0H0I9mEg0VLibqk,17963
nltk/corpus/reader/toolbox.py,sha256=TZQb8nICU9AGK3NazKIsN9Crpeu4GoMmtpPBYz4LAmA,2045
nltk/corpus/reader/twitter.py,sha256=sYULcBZiXuohL4rQ6aitjx51WQalZk4DVwUr6QL33o8,4472
nltk/corpus/reader/udhr.py,sha256=JNqv1EIzmthcDq0iWNKVpzZZNMcBah5jI0td5iCn3RM,2516
nltk/corpus/reader/util.py,sha256=ZZGqnVh1M5esWSm5rkeqbw-PabdjhZAsuszW60rQe8w,28068
nltk/corpus/reader/verbnet.py,sha256=BwJ-caLXB8B17EtfTrO9lxwjDfOqtGgQCOj5KcP5qHA,24775
nltk/corpus/reader/wordlist.py,sha256=ciYyo3_3onyh1KPd8okcfcSs_ABME7oA4vFx9ckzO7c,5646
nltk/corpus/reader/wordnet.py,sha256=F6j6i3V64jSt-L5TgsWPXun6bnlw1x5kyVyE-d6X2Pw,91464
nltk/corpus/reader/xmldocs.py,sha256=KcqOUzrDGwYYXhDMmbd-xIDVnaDFYLSipmPSXxJPX94,15888
nltk/corpus/reader/ycoe.py,sha256=uw3OxsBwjJVxA7hZCvlsl7jaA9LjbsTGeOmBbnzFaSM,10248
nltk/corpus/util.py,sha256=4QSdu3ePyBDMPf1G65FTVZE2gkH0kkFpVufw6YzkcNo,5712
nltk/data.py,sha256=1Q-w7oDKfqPa0BFB9VGiyhMwGeFfH1oCZXO_ztABCUw,54271
nltk/decorators.py,sha256=WyZ-QCZ0lfPWTvLSY-Hv4SmGgu-9_1vKCRAyM7lsV3M,8275
nltk/downloader.py,sha256=uxYWNHGLSwaNeNFM-6sKpFh35u8yQ0mG-4wd9QG0n_Y,92847
nltk/draw/__init__.py,sha256=itaUyapTThoyKf_Dl9cews_YqofvXapgKeJlKKX1NkY,783
nltk/draw/__pycache__/__init__.cpython-312.pyc,,
nltk/draw/__pycache__/cfg.cpython-312.pyc,,
nltk/draw/__pycache__/dispersion.cpython-312.pyc,,
nltk/draw/__pycache__/table.cpython-312.pyc,,
nltk/draw/__pycache__/tree.cpython-312.pyc,,
nltk/draw/__pycache__/util.cpython-312.pyc,,
nltk/draw/cfg.py,sha256=sgcx9H69xolqTsapPXjZHrplKDD3oJ809fjXuQriGqo,29933
nltk/draw/dispersion.py,sha256=CKCQjyQ0F8OXDHcOXrbonMqKwvcrdw3ZxeVTAnF4rBk,1895
nltk/draw/table.py,sha256=qDZZukClS0_vJJVy6eu51WmHHAZbg0kaa2638ymBLjE,45080
nltk/draw/tree.py,sha256=bLiz2_RdzER2smky7CTQrBRUiAP7kCCYYoDTPrucS_Q,38144
nltk/draw/util.py,sha256=VbTo_fHD5Hoje28pgjY08Ven4s1jRqIVyauq1ApbQmQ,88365
nltk/featstruct.py,sha256=LBGiwcegaoVtuJT0SbcaAZL6LS4U1VGyXmyg31uLQ_4,103297
nltk/grammar.py,sha256=r0r-ftcarH9IPzSm_G9qi9hYMF-ZFh2o81tFv0usvUc,57504
nltk/help.py,sha256=2a0YKnMbHUAB6PyzH5KufSk4vQVs5dAzkeJUN4-Ee0k,1775
nltk/inference/__init__.py,sha256=LaUMR-eFlEmtUTV5yutRzVKplz0dUgxss3g1xFHGohI,790
nltk/inference/__pycache__/__init__.cpython-312.pyc,,
nltk/inference/__pycache__/api.cpython-312.pyc,,
nltk/inference/__pycache__/discourse.cpython-312.pyc,,
nltk/inference/__pycache__/mace.cpython-312.pyc,,
nltk/inference/__pycache__/nonmonotonic.cpython-312.pyc,,
nltk/inference/__pycache__/prover9.cpython-312.pyc,,
nltk/inference/__pycache__/resolution.cpython-312.pyc,,
nltk/inference/__pycache__/tableau.cpython-312.pyc,,
nltk/inference/api.py,sha256=QlosgIHbc7B2JbWKxb2MYT-iK4diC0vP8ZlKaD23Ezo,18946
nltk/inference/discourse.py,sha256=fqjVA--gXLjq9ZVK2Mvy5u9vfltiVRNSMrYyvSckNwo,22034
nltk/inference/mace.py,sha256=5qoJpIlVdZOf1K7Q4j4TGzIcMUlUIeak88rPd4L-yQ8,11858
nltk/inference/nonmonotonic.py,sha256=a5kaFR-HRWS8qDlPQV75-d-1p7esnoE4G1iE9IUbaU0,18613
nltk/inference/prover9.py,sha256=O4_vKy6lGicua1top1rucLzP7wIEuf6M_1u2y2X4Hpg,15755
nltk/inference/resolution.py,sha256=VxRWqZRsQY-cW-5vsdOJpp1bycLfUjbmxbEY5s_dAog,26000
nltk/inference/tableau.py,sha256=mMNbcauvLe_75533Lgh_FSw8hJPbUxJCDMHoSHikVH4,25608
nltk/internals.py,sha256=VNvjB2T9vobL8QdX8SUOEiyzPI9QLGE3auBpZaju4Mo,38295
nltk/jsontags.py,sha256=b9z0lhRdMd6vefQUkqvtlWpUtmh0SyKKxeeQL0DzWuo,1883
nltk/langnames.py,sha256=P9uG-TBpneqThOfJJKmAveWXrADVT1B-NcaZAQl76Ao,17227
nltk/lazyimport.py,sha256=qhMSUI_-10Yjx62DGcpQMKcBHgpFEFaoWkFtyP_RWeg,4572
nltk/lm/__init__.py,sha256=GEsBkIi7c7QO9Y4glxu96hfMI35qXSe1ys6ckNo7Wl8,7816
nltk/lm/__pycache__/__init__.cpython-312.pyc,,
nltk/lm/__pycache__/api.cpython-312.pyc,,
nltk/lm/__pycache__/counter.cpython-312.pyc,,
nltk/lm/__pycache__/models.cpython-312.pyc,,
nltk/lm/__pycache__/preprocessing.cpython-312.pyc,,
nltk/lm/__pycache__/smoothing.cpython-312.pyc,,
nltk/lm/__pycache__/util.cpython-312.pyc,,
nltk/lm/__pycache__/vocabulary.cpython-312.pyc,,
nltk/lm/api.py,sha256=dXoBRUetvzJDcaulyqgFPxBrV_dU1BcnXHn0szbKIeQ,8410
nltk/lm/counter.py,sha256=Fq1grwzvPt8b0GuXSorijge1fpyGRB5oBU7UAtk8XoM,5087
nltk/lm/models.py,sha256=8nJke584qS8yM3C4UfZSeq43gGJuRK2I_1cao81H4KU,4762
nltk/lm/preprocessing.py,sha256=wHXlS0lFL2UBoqr6ZlC7cOdcKy8OW41n5WX6IURTAz0,1663
nltk/lm/smoothing.py,sha256=4vNg87_vox7suk5JyCCEgUA2efTpV23UYZ5JyZkPbv8,4618
nltk/lm/util.py,sha256=uQq6IE-29ZNNEQLLTSvOI1dQNU_s6c1yCog9ehz9k94,455
nltk/lm/vocabulary.py,sha256=p6s14x0BS143c8uaB3Smuik8dKyAp_wHKosMxRf5xsQ,6881
nltk/metrics/__init__.py,sha256=tast6hOS-8dAWHIDilk79sbWXcVfoZLbz5K_0hy1VR8,1192
nltk/metrics/__pycache__/__init__.cpython-312.pyc,,
nltk/metrics/__pycache__/agreement.cpython-312.pyc,,
nltk/metrics/__pycache__/aline.cpython-312.pyc,,
nltk/metrics/__pycache__/association.cpython-312.pyc,,
nltk/metrics/__pycache__/confusionmatrix.cpython-312.pyc,,
nltk/metrics/__pycache__/distance.cpython-312.pyc,,
nltk/metrics/__pycache__/paice.cpython-312.pyc,,
nltk/metrics/__pycache__/scores.cpython-312.pyc,,
nltk/metrics/__pycache__/segmentation.cpython-312.pyc,,
nltk/metrics/__pycache__/spearman.cpython-312.pyc,,
nltk/metrics/agreement.py,sha256=DPqE5p8IlNdWI5RsvVfMuB7FwRGQcgncnj3L1bnWLE0,16104
nltk/metrics/aline.py,sha256=cxJ9MuNGTUjaQ11BqhxBNscNo8OHnKb0ujouag8fdzo,31627
nltk/metrics/association.py,sha256=nV2QNXs5zXuCOQQpeMpp8sMWYrK28Kc1Ie9KR5WUx7I,16093
nltk/metrics/confusionmatrix.py,sha256=vddvxkPxRO54amwHCH0Q3MYhdDG5KQIT2EGv7PWvy3E,12679
nltk/metrics/distance.py,sha256=415iowoA9IqF-3bukmPX-HsuwM0qowRp66pVMSxDFr8,17153
nltk/metrics/paice.py,sha256=oUOgag9-SGQ4Hx82-qQogqQ7nugyZxyWOyGMXC7mxU8,14350
nltk/metrics/scores.py,sha256=Bv7AowtCO6IN4KcA-pXSdlOacmi-QVQRh60S3wDRPTg,7694
nltk/metrics/segmentation.py,sha256=Dh7WIT3zNwrN5f4e3ss18fibVQloEJN0sK2KrLdfoIk,7006
nltk/metrics/spearman.py,sha256=OVU0Gxlfc7Hb4nIp-xLPsqRqF4GJ1lNa8HCZP1A5uOc,2129
nltk/misc/__init__.py,sha256=MhGRtzz66k91HIg-zNK-S2pWr9fe15B-m6erKjfTY0I,395
nltk/misc/__pycache__/__init__.cpython-312.pyc,,
nltk/misc/__pycache__/babelfish.cpython-312.pyc,,
nltk/misc/__pycache__/chomsky.cpython-312.pyc,,
nltk/misc/__pycache__/minimalset.cpython-312.pyc,,
nltk/misc/__pycache__/sort.cpython-312.pyc,,
nltk/misc/__pycache__/wordfinder.cpython-312.pyc,,
nltk/misc/babelfish.py,sha256=DsLvTdVbE4CqSL_rJ9oB-0zqmitZ7gH8xKIlQ3bcSgM,351
nltk/misc/chomsky.py,sha256=PmwBLQSnLebvLfbJcebfBiY3N109ADetAmz-gQxi9Oc,5185
nltk/misc/minimalset.py,sha256=gzkbHuwDcjozuDFNyu9w2r6QexUeyyz_CBzEWrWCS7s,2894
nltk/misc/sort.py,sha256=JGTOryiLphZ4Cl2HrdSQlS-rwiyfw4uN88vtlgZqi_E,4371
nltk/misc/wordfinder.py,sha256=EUNLcNv85qNjsp_c5kY3tzfUwHEVSLwXzQoNNXM6ZAk,4213
nltk/parse/__init__.py,sha256=YBQNXROGql_QZXTPYsUE3EcxqLiAH0jfaE6uzG_QbMg,3695
nltk/parse/__pycache__/__init__.cpython-312.pyc,,
nltk/parse/__pycache__/api.cpython-312.pyc,,
nltk/parse/__pycache__/bllip.cpython-312.pyc,,
nltk/parse/__pycache__/chart.cpython-312.pyc,,
nltk/parse/__pycache__/corenlp.cpython-312.pyc,,
nltk/parse/__pycache__/dependencygraph.cpython-312.pyc,,
nltk/parse/__pycache__/earleychart.cpython-312.pyc,,
nltk/parse/__pycache__/evaluate.cpython-312.pyc,,
nltk/parse/__pycache__/featurechart.cpython-312.pyc,,
nltk/parse/__pycache__/generate.cpython-312.pyc,,
nltk/parse/__pycache__/malt.cpython-312.pyc,,
nltk/parse/__pycache__/nonprojectivedependencyparser.cpython-312.pyc,,
nltk/parse/__pycache__/pchart.cpython-312.pyc,,
nltk/parse/__pycache__/projectivedependencyparser.cpython-312.pyc,,
nltk/parse/__pycache__/recursivedescent.cpython-312.pyc,,
nltk/parse/__pycache__/shiftreduce.cpython-312.pyc,,
nltk/parse/__pycache__/stanford.cpython-312.pyc,,
nltk/parse/__pycache__/transitionparser.cpython-312.pyc,,
nltk/parse/__pycache__/util.cpython-312.pyc,,
nltk/parse/__pycache__/viterbi.cpython-312.pyc,,
nltk/parse/api.py,sha256=kOWCJ4TFR6LPJ_pspjboRrYNfcv9d2cHhO8xK9kCAb4,2282
nltk/parse/bllip.py,sha256=loCOIjaj3zGL2LcAwIqYfDTV6kSMnAteAlqYs3NOcfs,10677
nltk/parse/chart.py,sha256=Y0HWhD_CjOVonYm4yTqi5ur4SKCIJQIBzsAoLyZRmKA,61908
nltk/parse/corenlp.py,sha256=MuzPBBlO8BBUhsMtEX9872L8nXhuoyRazI8ckrUuAeU,26941
nltk/parse/dependencygraph.py,sha256=eH1Gh8QdwpY3gK5xioZFYP-_yM3SC_HXy8_UovtqN6E,31669
nltk/parse/earleychart.py,sha256=NlNHvtHBKixWknEe4hM-iR3xE7gFelR-G2_a-mGKBok,17718
nltk/parse/evaluate.py,sha256=naaLk33IQGBiMxz5H3J3G_TK50tBKp0DJ9oXrOHVBj0,4339
nltk/parse/featurechart.py,sha256=EeTyymSmVaFMSAPXDaucgme00tg_tdxO1l9moEhQYjs,21856
nltk/parse/generate.py,sha256=b-wGPTJJuTBwBXTD9YGvhmH0CE3EbH13t526cBDVJxk,2498
nltk/parse/malt.py,sha256=ZIgNrxPMyMvAeZnFoCuYVA2XRU7ozQlOLZiTH5a8qQk,16178
nltk/parse/nonprojectivedependencyparser.py,sha256=votlIc-907dHe1pTcVjcYOeDwHrsXUl4ukxmq2A0RUI,28674
nltk/parse/pchart.py,sha256=pWz_uPCyT63V-iuN8ViXVqhUK-7XAnxNbakQOmI6mJk,19901
nltk/parse/projectivedependencyparser.py,sha256=Lp1sHWIFV2VJX721pS3quhvqZPL-JK0Mc3uz2Qa0gOM,27527
nltk/parse/recursivedescent.py,sha256=NU_Vq74gFSGi9pd2wgPE88Z4wo5XTMKAz9udOEjyOpQ,25348
nltk/parse/shiftreduce.py,sha256=wo5pdgtcw-a7Fhx_L7_TLTt4MdQQc6tjSgc2KRVnPsU,16591
nltk/parse/stanford.py,sha256=RRM8yxDwP6LgbRNdnP8Wp1RyKF0zL0Xri6_Y7jE6zvU,18840
nltk/parse/transitionparser.py,sha256=LNZYLVUjK1NzoD4BQFBgkcb0B3wKIRlwKkZcfw1wUsI,31463
nltk/parse/util.py,sha256=jWjk956uXXzBcTxMS68XpDEzIT7WssXn-tyioCQT8Q4,8431
nltk/parse/viterbi.py,sha256=bAA0wVgnEH5Kse766wb_wcdxrrrkBBktHzhfdkuZLQ8,17896
nltk/probability.py,sha256=wDdxVLde3BVf06SogcRaEpotWDVzGwj-dNHSj6EqUPw,90323
nltk/sem/__init__.py,sha256=5cmuvwOjRQcpM0zTSYe6zukLYF-IGKT6TIOUmNsgGCE,2368
nltk/sem/__pycache__/__init__.cpython-312.pyc,,
nltk/sem/__pycache__/boxer.cpython-312.pyc,,
nltk/sem/__pycache__/chat80.cpython-312.pyc,,
nltk/sem/__pycache__/cooper_storage.cpython-312.pyc,,
nltk/sem/__pycache__/drt.cpython-312.pyc,,
nltk/sem/__pycache__/drt_glue_demo.cpython-312.pyc,,
nltk/sem/__pycache__/evaluate.cpython-312.pyc,,
nltk/sem/__pycache__/glue.cpython-312.pyc,,
nltk/sem/__pycache__/hole.cpython-312.pyc,,
nltk/sem/__pycache__/lfg.cpython-312.pyc,,
nltk/sem/__pycache__/linearlogic.cpython-312.pyc,,
nltk/sem/__pycache__/logic.cpython-312.pyc,,
nltk/sem/__pycache__/relextract.cpython-312.pyc,,
nltk/sem/__pycache__/skolemize.cpython-312.pyc,,
nltk/sem/__pycache__/util.cpython-312.pyc,,
nltk/sem/boxer.py,sha256=Z1g6Lvyt-EgtLxcl8WdGJnZZy-pKwUfXi0MrUBzaeE0,53740
nltk/sem/chat80.py,sha256=ISwSFu_UAqz-7pxBco_LZpyPYsvsPI-PM_DtCbxfQHk,25656
nltk/sem/cooper_storage.py,sha256=oIp_alL3A0ah5nx7cATU_pf0283xV8HVf_Q8WcqFryE,4086
nltk/sem/drt.py,sha256=OdyfKJ2k3rLC6rfkiYvMJNhAw0tqeZm-YtA7mcIIAzA,51683
nltk/sem/drt_glue_demo.py,sha256=YdnrXBgbinHlwpoepsrMM5fcSR4T1CqS7hnOEqf1Zgc,18618
nltk/sem/evaluate.py,sha256=60oJUWdjmMKdgxM6v8YcY92PasZfcMuI6jZ7dqMHvW4,25446
nltk/sem/glue.py,sha256=mBZuLURUdXqECQTjW1_0boOzO73Fy5o7Kvnr3s4a1DM,29415
nltk/sem/hole.py,sha256=zv1UX7azwhnwYXg5rldpn2Z8gYxdDgXeSPdO3Q55QsI,13821
nltk/sem/lfg.py,sha256=ZCH1R1FsJ32jkRIoQb8dP2CGveuHvjmMqeWYJ63cmzs,7455
nltk/sem/linearlogic.py,sha256=0HMdElxMtn6TuWDJZJ8BKqvStuvKwsgyWTSYVwgB3PA,16749
nltk/sem/logic.py,sha256=NWC9PwCD1Ee0q5jWkOogWb5lzYgiYmXMA5CJ0_-Wf4s,68174
nltk/sem/relextract.py,sha256=mfyDPeWksDbf1e74Zw7174teYXVbKDnFQ8S_3GmZBT0,15270
nltk/sem/skolemize.py,sha256=zAwfjvbg_6eODwTYroEQPGjx3YmRyx7uzxRDG94r_Gc,5722
nltk/sem/util.py,sha256=EYrJB1jkPhZ7HmheR_aZh5PixZe6T5SIMDTyNWxayBM,8747
nltk/sentiment/__init__.py,sha256=HyLcxuFdali1DEti6BrMC5gmI5leOyoRDq1Yfu6NP9c,369
nltk/sentiment/__pycache__/__init__.cpython-312.pyc,,
nltk/sentiment/__pycache__/sentiment_analyzer.cpython-312.pyc,,
nltk/sentiment/__pycache__/util.cpython-312.pyc,,
nltk/sentiment/__pycache__/vader.cpython-312.pyc,,
nltk/sentiment/sentiment_analyzer.py,sha256=9FHNrKGdR6NviLSHi21Vuj7F9m2y47Vzcbom4TO1y5k,10177
nltk/sentiment/util.py,sha256=_4dm0rstYn9zvImJBEtCLZmg2HKpVi61D3ERfauYKCg,30392
nltk/sentiment/vader.py,sha256=L0iggwqTyDqp_Xr0_CBAGAtFy3EbPUIAG87VbXPfsxM,21131
nltk/stem/__init__.py,sha256=rPbJW0kpudLfemyEaylYZ7XbtDcjloO91utsrq1wXro,1262
nltk/stem/__pycache__/__init__.cpython-312.pyc,,
nltk/stem/__pycache__/api.cpython-312.pyc,,
nltk/stem/__pycache__/arlstem.cpython-312.pyc,,
nltk/stem/__pycache__/arlstem2.cpython-312.pyc,,
nltk/stem/__pycache__/cistem.cpython-312.pyc,,
nltk/stem/__pycache__/isri.cpython-312.pyc,,
nltk/stem/__pycache__/lancaster.cpython-312.pyc,,
nltk/stem/__pycache__/porter.cpython-312.pyc,,
nltk/stem/__pycache__/regexp.cpython-312.pyc,,
nltk/stem/__pycache__/rslp.cpython-312.pyc,,
nltk/stem/__pycache__/snowball.cpython-312.pyc,,
nltk/stem/__pycache__/util.cpython-312.pyc,,
nltk/stem/__pycache__/wordnet.cpython-312.pyc,,
nltk/stem/api.py,sha256=owmPuT6PrjwnuZGazMEQbinjmHSoyWhkYwxJ9efzbG0,714
nltk/stem/arlstem.py,sha256=AG-Yf-nUGwGCkqisS2wDNM_B9Sdzp3q2HLXm0TAEKTw,12645
nltk/stem/arlstem2.py,sha256=6Y1hNUhVwjcj2oDnzXdjgOC7K_WZcGJDPphficHnVQ0,16078
nltk/stem/cistem.py,sha256=3hJ2lPImwq6vWrN3rN9UrtFNOXIh4zlCcO2xcHSaEL0,7050
nltk/stem/isri.py,sha256=P70lZaMFy3x4S02tZiRIzgIHniP66-stN9kJk_A14ZQ,14595
nltk/stem/lancaster.py,sha256=5S6AAHr4Bg1c7cY-ehUSMAih2wRnb-O4gNgb2aJZXCU,12243
nltk/stem/porter.py,sha256=X1KQyVau6aUjMOolNaaDF7JbIGEpIMK7ubtCpi_3_nc,27711
nltk/stem/regexp.py,sha256=KHN4ffss1H-S3yK10UhjcPyHdFAKuZdh82qo_-QjLnw,1521
nltk/stem/rslp.py,sha256=GjUt2_wx88rQsb932gFAMfWf_a3AVIRf_XXAtkl_UEc,5374
nltk/stem/snowball.py,sha256=nWqb445vPRaTVyiZ8CcEs_qgTu8Rm0_mulOk_-bTKt0,177919
nltk/stem/util.py,sha256=PBaiiQxw5WD0ppVlq41aAg5KuzwbSm7cPYAkHHXPr1c,619
nltk/stem/wordnet.py,sha256=RMZ5v0kzcGPPX_JqyD-MMHO-VM2Ed6JWDi13GrhysG8,2884
nltk/tabdata.py,sha256=U_qI0mBsj6_K55MKH7Nn9QPhdWvTPbNXlG_ljDT7niI,2601
nltk/tag/__init__.py,sha256=vSRtwl8FY3joCnPfCgnNFmJMdjhmrfOf4mhE9EQRshs,7088
nltk/tag/__pycache__/__init__.cpython-312.pyc,,
nltk/tag/__pycache__/api.cpython-312.pyc,,
nltk/tag/__pycache__/brill.cpython-312.pyc,,
nltk/tag/__pycache__/brill_trainer.cpython-312.pyc,,
nltk/tag/__pycache__/crf.cpython-312.pyc,,
nltk/tag/__pycache__/hmm.cpython-312.pyc,,
nltk/tag/__pycache__/hunpos.cpython-312.pyc,,
nltk/tag/__pycache__/mapping.cpython-312.pyc,,
nltk/tag/__pycache__/perceptron.cpython-312.pyc,,
nltk/tag/__pycache__/senna.cpython-312.pyc,,
nltk/tag/__pycache__/sequential.cpython-312.pyc,,
nltk/tag/__pycache__/stanford.cpython-312.pyc,,
nltk/tag/__pycache__/tnt.cpython-312.pyc,,
nltk/tag/__pycache__/util.cpython-312.pyc,,
nltk/tag/api.py,sha256=pOEL75o_eE0CwF1i0yQtlq1zDz2wsQDNb6EyOIKclLk,14514
nltk/tag/brill.py,sha256=BLew1-MH1w7c-YHvRv_pLUD1CL8n2jwgkXmIVbzOdhs,16368
nltk/tag/brill_trainer.py,sha256=__ViKxzUQDXJfcnFlb_inTIXoo5xWWxtiIhk4KjzHYE,27268
nltk/tag/crf.py,sha256=7b6DmASmB-U_aRuPF3QBluTZxH3BxN5-T4NcPhPArcI,7753
nltk/tag/hmm.py,sha256=ONJbgYmNPeW7lL9Rexq-90xqvI5J847-0MPlP2RAGug,49017
nltk/tag/hunpos.py,sha256=LJY4ssxRV_R8I7T4W5KNTGn-MZbN8dk5WnW0r6XEH3k,5053
nltk/tag/mapping.py,sha256=m-JzS5GB7m6KTpxYLQkw6CC551A_uDu859TP4C-46f4,3888
nltk/tag/perceptron.py,sha256=fRVlWKsduPGkLODSu3tyGRq5FAbnQdlwHZZjfiIgs5Y,14295
nltk/tag/senna.py,sha256=7P4fw-wOXve-RWDfzBKHz5_aMgSjAYn6sVDQeYHv_jI,5769
nltk/tag/sequential.py,sha256=R-AGezHZSI5yh2Q7_1TQzmr_leD3hyZex9rffvi50mE,27865
nltk/tag/stanford.py,sha256=Zgiez0lT5US0kHaKiYItkWLKwKJBjT6SQrp1HU5H79g,8191
nltk/tag/tnt.py,sha256=5pZb7S82dsGhT905wy9GboK-KRj4fX95ipPiZrPG2Z0,17844
nltk/tag/util.py,sha256=5g7ZIL2Q7Z7DrBUjobY9fHqd148BqjSPMUHskYF8g8o,2281
nltk/tbl/__init__.py,sha256=0T4VKd84PGUqzHMt1MYOxIhHMKz4W7pultRrv2GjG7o,759
nltk/tbl/__pycache__/__init__.cpython-312.pyc,,
nltk/tbl/__pycache__/api.cpython-312.pyc,,
nltk/tbl/__pycache__/demo.cpython-312.pyc,,
nltk/tbl/__pycache__/erroranalysis.cpython-312.pyc,,
nltk/tbl/__pycache__/feature.cpython-312.pyc,,
nltk/tbl/__pycache__/rule.cpython-312.pyc,,
nltk/tbl/__pycache__/template.cpython-312.pyc,,
nltk/tbl/api.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/tbl/demo.py,sha256=aKAA0g9a01q_JBhLffC2FZarW1fkXoh0DzzG3zHb24w,14918
nltk/tbl/erroranalysis.py,sha256=GQ0FLt5Xn8Zq2tKjbeRfQ-KVlguOsAkSqfWHYTrMZL4,1414
nltk/tbl/feature.py,sha256=BloS_NXP2PBbLsIO57XDwA1bIuCaIkOw4yYJOSdak8c,9423
nltk/tbl/rule.py,sha256=7-wBlWTN9-MAOq4QDJoi8CyA_29FNDmsAJn6b1qwZGA,11188
nltk/tbl/template.py,sha256=qvgjWGEQvwMSZ7nWVlHa7hMnIWyBj9fb5GaawgUSIc4,12613
nltk/test/__init__.py,sha256=EVmvafhfMtSrMvY1XsmNBfVXEm7Y28Al2mm2wil5W1Q,469
nltk/test/__pycache__/__init__.cpython-312.pyc,,
nltk/test/__pycache__/all.cpython-312.pyc,,
nltk/test/__pycache__/childes_fixt.cpython-312.pyc,,
nltk/test/__pycache__/classify_fixt.cpython-312.pyc,,
nltk/test/__pycache__/conftest.cpython-312.pyc,,
nltk/test/__pycache__/gensim_fixt.cpython-312.pyc,,
nltk/test/__pycache__/gluesemantics_malt_fixt.cpython-312.pyc,,
nltk/test/__pycache__/portuguese_en_fixt.cpython-312.pyc,,
nltk/test/__pycache__/probability_fixt.cpython-312.pyc,,
nltk/test/__pycache__/setup_fixt.cpython-312.pyc,,
nltk/test/all.py,sha256=-qE4Z8bu-2m4j7BMFNEvmP0di4snndRGMzpeeaTqZL8,795
nltk/test/bleu.doctest,sha256=ZNq4bmCh8YDh4n2NCw_sbIlnpJ8AgExsXquvybojW7c,833
nltk/test/bnc.doctest,sha256=i9jpF5fZQLLUwnCING9RwaPh4aVoz6E9jf3DlsKHAsc,1991
nltk/test/ccg.doctest,sha256=2QNRF3NgVxyJ5wPed8X5qpOA7_lHWNKdhvT0F79gJSk,19410
nltk/test/ccg_semantics.doctest,sha256=nR4RWcK9O1UfzAumDhgtdjI1GkGPt_sRAqMNp12b2to,30514
nltk/test/chat80.doctest,sha256=C0jLMsmLaQ6uuujAzbVv10PXKNYhNVyXbq55yo9JPCU,8503
nltk/test/childes.doctest,sha256=cKCWCQXf1juDiZLHYkM8XHWd_7j2c0lMvfFMX8akedg,9173
nltk/test/childes_fixt.py,sha256=zTfrv_Qgx2bg6XSHMqkUSn2d2VvxdPWG80ltY-3aKUc,359
nltk/test/chunk.doctest,sha256=NDU7KPRSLb0fP9avbTGlnRdKKXXimR4agvpDZoqGaYA,11139
nltk/test/classify.doctest,sha256=2Bzd5n9tTWjSitTRNcuENCOyinzOgSkbyVd0l07_lGg,7497
nltk/test/classify_fixt.py,sha256=ien3kdMqWZl2jd_EDX7kwv_3acv5NniKWSh51Ca1pbI,114
nltk/test/collections.doctest,sha256=YajV4imXRt6Eh257LbGeWLYPhS7hpdwj4sG3MJREDU0,591
nltk/test/collocations.doctest,sha256=z8DxQMPGU8j6KS1xdbRoreMHgMC0gqEeTBi1UKAt_e0,12199
nltk/test/concordance.doctest,sha256=jvyA7kMNyMCdkQDNR8kqsz4dwH3c6tfCnpg2MZebZ5Q,3469
nltk/test/conftest.py,sha256=iXhN5hecXQ713Zdq1cfkYyTIyGFULgCeFOB4d-YV3sA,771
nltk/test/corpus.doctest,sha256=Agx3Hb-wGRHnegDC0OR6ozN3_t_w717PFHXLT-btLXQ,101376
nltk/test/crubadan.doctest,sha256=-z_WQmWf7z0N2TLcmLQ1IL5TAgL8GDHBr07rYxYBXcE,1995
nltk/test/data.doctest,sha256=ftM1r5ZBRV40DmEl5NEfj_VTtmAP5yMZMzEM1kgt61Q,13953
nltk/test/dependency.doctest,sha256=Gjv9EjQ_RglZXqR0qzfIIPELFVjzm9rIghFPzWLrX7A,7428
nltk/test/discourse.doctest,sha256=wpMLB_o8VPfq8tGoES6xdkvRtsgmgUGSvjy9ReRdRfs,17371
nltk/test/drt.doctest,sha256=JIIkkShDZeL6uJxdpvyHVAEBNiDy2y4krk3MAq59nRU,19561
nltk/test/featgram.doctest,sha256=naSq3BVI44bs-2_RBXvTYcA4EskjC6Lsf9KUaQfoJr4,28260
nltk/test/featstruct.doctest,sha256=NOA6LpMI7NZ1YF5F2ZNtHFa_yumlhe3wjZI3bT5xfVI,37665
nltk/test/framenet.doctest,sha256=vxXEl3TqqegslSqKQCiznkSxymL6mRvGL3bQTu-lBqU,10509
nltk/test/generate.doctest,sha256=xjqVd2gzpOB5RBEu88YGhFyqFVjCzzTwK_2fDliYwpA,1972
nltk/test/gensim.doctest,sha256=GXLZUYVQLxRGFQXsYteKECPCVsl9kRqubx5awS-I5_4,5059
nltk/test/gensim_fixt.py,sha256=SnLRcbEVQhnbG3xzIRgmyAW2aaxzvACu2cuc2bGxiA0,73
nltk/test/gluesemantics.doctest,sha256=oLMFemTbViOjsczTdEDnsZEK0EzebUMwJxTrwm5eS9w,12322
nltk/test/gluesemantics_malt.doctest,sha256=N5sCeo7CHTZXKQMLXnMocI7lDeMS-6mFeI5TQd-OAYo,2598
nltk/test/gluesemantics_malt_fixt.py,sha256=mzeqijjUl562AXj1zvWBNAwkm-m06nL0FgcPTlBnyl8,223
nltk/test/grammar.doctest,sha256=5b2lLkoWRkoWQMOa2FjVlb_iDy0CFQ_jpki0078feB4,1880
nltk/test/grammartestsuites.doctest,sha256=mbLMbAqejyxmXJItAHUNzja-ZgvfxCto_GbzqCn2AaY,3200
nltk/test/index.doctest,sha256=bWJJHJeuPLjJXu9nrFKGc6o44-uCnKnz5W27bEjc93E,2601
nltk/test/inference.doctest,sha256=lLSEm-3wv9QiERlhdCQdub0y7ypT_SaV-Q7vtaqMcWE,17829
nltk/test/internals.doctest,sha256=HIzaSeomrxHSOvNJY6hdDQqNpUc_IHmG_TIPVHu9kHA,4122
nltk/test/japanese.doctest,sha256=pBfhZVYkyIrBT8dFm0bJ4BI00ba5BMuTeTdnIH6oe7o,1045
nltk/test/lm.doctest,sha256=FIW2E--Fkh0mW8vQ9kpGKaAXfK90qd0SVq_jJhwgyL4,3816
nltk/test/logic.doctest,sha256=pbKKU85aamAvPzElOKO-yrP4bg1S-8V9cS-tCsxdciA,34087
nltk/test/meteor.doctest,sha256=h1Qj_dCjiY8aziNXh6ur5nItsAK8u44qrOhBRcS-apo,1469
nltk/test/metrics.doctest,sha256=8BSKJXLEjOUttjjQFQoOkX2KNVKBH3p_n74pmyWNDjE,10962
nltk/test/misc.doctest,sha256=n57g_iaROuTcrUQy7iGOYoaA6KHiC--Rn_HliTuu9KM,3346
nltk/test/nonmonotonic.doctest,sha256=3v2v97NFrH3j1wmnilIG6boj09NYqz6FVsW0Pqxp1SY,10077
nltk/test/paice.doctest,sha256=OM6l7fOhifdG3BFdg-ulhdlAnZasDP5ok5semlh3jME,1238
nltk/test/parse.doctest,sha256=1Tv63ftBcr2BarUGaDfx66YBrVreXAOE4CpcPsRqC0M,34003
nltk/test/portuguese_en.doctest,sha256=EdcydgyZR-SMDW51ku1feJFJEPPaFMpj9W_ytuDoAPY,22663
nltk/test/portuguese_en_fixt.py,sha256=eMpiVYNBTzCZDlvKYpRs5A1xGIu7GezIk53IS4sn68Q,126
nltk/test/probability.doctest,sha256=FiLHchfBGuc8X6LaGhKeAJ-vq0-LEqN1CmpegzKqfRg,8938
nltk/test/probability_fixt.py,sha256=S8yPcNMYXBYfSgLwqZ-4LLIxiieyrjV5EDRvn5ydmas,180
nltk/test/propbank.doctest,sha256=vEVlCoRotSnP8D5XrPWCvVP2PRm_7W1PTchvRXDKyJI,6518
nltk/test/relextract.doctest,sha256=HXW35m7RgelTORNTmHVuap9P-cn-kfEbjCfl_smYXC4,9257
nltk/test/resolution.doctest,sha256=4uWUOm3ETnENadE-w_1ZSCZEXpGOfhH29TOq4ap8QzI,7788
nltk/test/semantics.doctest,sha256=VV05UssX8gs22iLHi90V51IQDmgF18UdLSX07tnu6iQ,24523
nltk/test/sentiment.doctest,sha256=7NBCKitB6dLi0VgMyJ_em1mNcMB6AD5Y2v7x3FxwnI4,11993
nltk/test/sentiwordnet.doctest,sha256=zROV5-ptmjilB2ZTSfk9c1B9CLMPxw0HBtXSVhoDIkw,1010
nltk/test/setup_fixt.py,sha256=XnR5Esl_R0XiKIEuf9CzYBqIZ3Eo3qG0ai6YNzej0Vs,886
nltk/test/simple.doctest,sha256=SY_RRxtk3L3RuiJxZrdlo5Lr9zRBFJGLpxGovBLxSk4,2324
nltk/test/stem.doctest,sha256=UNkcSkg6S_qDbJvetNCqBPrjBW0KEebBQjZhN5nEfII,2447
nltk/test/tag.doctest,sha256=MnrrOzmlrNjjm4b92XrFPdFvF9RaBy67x1kqTs0Y9yA,33786
nltk/test/tokenize.doctest,sha256=sOXDPaS_ql4bgCJLsnalmTbFmnXmsLC0VfkVeBwUex0,22147
nltk/test/toolbox.doctest,sha256=x8cDQbwPm0xlJkY4VSZ30vONf2GDCVjBctnKh7QCg1A,10017
nltk/test/translate.doctest,sha256=NHeGDXmirmh_0G_ws1j29EBdtmW_T5C4nNLbSrOqSco,8156
nltk/test/tree.doctest,sha256=K7XKxTNjlE2yj4aIkJ8moKRdUTXf1Zil6E9-iyCTKEk,46050
nltk/test/treeprettyprinter.doctest,sha256=b7460RnadhLrIME1Cl4lwZbTa-y_Sv48UMD_5p2iyJk,9199
nltk/test/treetransforms.doctest,sha256=gA5d0nmt4UfqMet7lxVA2ZcOBdzbea3esj_2v2SFaS4,4852
nltk/test/unit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_aline.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_bllip.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_brill.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_cfd_mutation.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_cfg2chomsky.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_chunk.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_classify.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_collocations.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_concordance.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corenlp.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corpora.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_corpus_views.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_data.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_disagreement.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_distance.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_downloader.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_freqdist.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_hmm.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_json2csv_corpus.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_json_serialization.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_metrics.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_naivebayes.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_nombank.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_pl196x.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_pos_tag.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_ribes.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_rte_classify.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_seekable_unicode_stream_reader.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_senna.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_stem.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tag.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tgrep.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_tokenize.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_twitter_auth.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_util.cpython-312.pyc,,
nltk/test/unit/__pycache__/test_wordnet.cpython-312.pyc,,
nltk/test/unit/lm/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/lm/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_counter.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_models.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_preprocessing.cpython-312.pyc,,
nltk/test/unit/lm/__pycache__/test_vocabulary.cpython-312.pyc,,
nltk/test/unit/lm/test_counter.py,sha256=D8lw_-N0EvBCjBkmDICgP_SoNCri67fx9b0ANAWs4IY,3775
nltk/test/unit/lm/test_models.py,sha256=QMwz_9iDbbfA0eyjeipp7sLjOr7KEXwWrvbUqAfzf9w,19579
nltk/test/unit/lm/test_preprocessing.py,sha256=-NgKCZX0_7A-a3LL8akBVAWm2-8Li2ukdFns_tVqUAc,969
nltk/test/unit/lm/test_vocabulary.py,sha256=TNqu-3V_5_g3sjXKGFv1E2SEsZ00rtgWEboByEr7yOA,5761
nltk/test/unit/test_aline.py,sha256=Im1PJmoevSmPY4AljLWju2vVQngH4L-YV1HWvSBMNDk,1083
nltk/test/unit/test_bllip.py,sha256=cMhbDVJckuCN22ja6keh7968Qey0h5nkp44Q_IoWLAw,1073
nltk/test/unit/test_brill.py,sha256=G1VyOa4Z0ncohJBsUFE0AqknpaBYJnsKIIMsIShRF2k,990
nltk/test/unit/test_cfd_mutation.py,sha256=5AwXtHdtTMdHIyZXIJPqiDHSkoRvpZh9mjQJVa79Joo,1334
nltk/test/unit/test_cfg2chomsky.py,sha256=MRuo4rHZyRo4bXOvQDQLEJ3lCBdb6UmZHCBojZrKDcY,1677
nltk/test/unit/test_chunk.py,sha256=0xxFRAD0FvZMzrCTjWCGEZYXJIzcX1GqsxIrhQtyE5o,2134
nltk/test/unit/test_classify.py,sha256=zIbViWdHVdgPnG0ZBu2XyulL9OE5mFYx0Y9bAy6vjts,1289
nltk/test/unit/test_collocations.py,sha256=tI6AVKX2vfwd22SZz0P2CW9vFx1Kcsu4HfjxY-iR_Lo,3570
nltk/test/unit/test_concordance.py,sha256=Px6oGsa019LY-OtCE4xwPciFOKgPr63jeKONvKaJ-Go,4010
nltk/test/unit/test_corenlp.py,sha256=j2utOVM2pFTXTZTVzPJHxY-5S4e3TaXVRf3j4sg-R60,57196
nltk/test/unit/test_corpora.py,sha256=7xsgDP4EcCgc9p_TpAvo3SDoYZOSQbS4Z7sfcC4kY24,9662
nltk/test/unit/test_corpus_views.py,sha256=YzsD9rmS1fdnM1gaQoB1fWVYtmCyqYQsaQGrYIEt_to,1552
nltk/test/unit/test_data.py,sha256=ZZuRJ-t0bSGdkol7NECc-LkqyiA9GzAedtzfyhHeHTw,375
nltk/test/unit/test_disagreement.py,sha256=MsGXqiqtpgFaCYlqBb2s1-L5sA3FvWMuLa0RNV_eEg8,4874
nltk/test/unit/test_distance.py,sha256=y67Y_bLV-f3mEHN67FoPx-zv4SIvvqCiM9VCZHZ_BzU,5710
nltk/test/unit/test_downloader.py,sha256=yaL8yPkPzO2vizxkp46cBxX1-gQntBpapupKKEtHgZo,722
nltk/test/unit/test_freqdist.py,sha256=hJUkutZZjyARsgOf55lhRWBxvmcnGTrXXOu180WzwWE,203
nltk/test/unit/test_hmm.py,sha256=IX2vGsl0nJNQUrHxiL7zM8L81Vcc61NsyVH5Un0NDEA,2203
nltk/test/unit/test_json2csv_corpus.py,sha256=Q3bVQoxThW7RyYd08bNl61whw2kDFwpUT7oRvDwOxtc,5678
nltk/test/unit/test_json_serialization.py,sha256=YiN5k3JXNxc0-xfGLgBqjtywXxqvVfWFYf2RcH7l4nk,3539
nltk/test/unit/test_metrics.py,sha256=mb7sTzy1qkQRUR0yZWbDD8RO3ZNTXj4x5yLPLbB-Yws,1883
nltk/test/unit/test_naivebayes.py,sha256=XpzZQpr3BSnm2w22F9bymvDh0SSx1FJTnVLPLYa57hM,743
nltk/test/unit/test_nombank.py,sha256=niGOnyPLDlaOd1RPHa2dm9MOkD7I5iRUZ8oMDg_tRUg,733
nltk/test/unit/test_pl196x.py,sha256=ysKjSVrXsG1PZWl1ZxaL7eKN-ahbvQkhRcRPaQ864jI,397
nltk/test/unit/test_pos_tag.py,sha256=SmbtemJDgj2drCtSlW6CCpHwFCHkR7OWHFnfXBoI62g,3895
nltk/test/unit/test_ribes.py,sha256=fBODH7pFBBK1j2V8gS71NcPm_5EALJJgQChETqTgOsw,4958
nltk/test/unit/test_rte_classify.py,sha256=XBlIJPj6XhmSwSa-YRz_jDUbp7YMUO7APUEcm5OFK2k,2671
nltk/test/unit/test_seekable_unicode_stream_reader.py,sha256=x583-Wlqeb94dnOfzy8SesrDw4KQfAc2Epp17M8Mpvw,2179
nltk/test/unit/test_senna.py,sha256=nEdPDapYE_StHLB5yuWibnIIA6CpnxV_bUMAAknJHCw,3600
nltk/test/unit/test_stem.py,sha256=IpuS0VaJGptLqrD1Ak3oSN1Fgb21kadxRkuGMEVE4do,6190
nltk/test/unit/test_tag.py,sha256=pv-hoLndPgjGG9Z0cpp_uWAyrG5jTFg_CPLSMIW4ln4,512
nltk/test/unit/test_tgrep.py,sha256=DhUTP-G_V2bXqJuzDfKKokVqD1kMDVGTSocvnohUjHA,30927
nltk/test/unit/test_tokenize.py,sha256=Ol2ZQvMnj1mXJNNBCGQDKe7weE7GcUeZJE5bgHsyIOg,31048
nltk/test/unit/test_twitter_auth.py,sha256=mvlaKdYBBkouuws-gGArDqf6dk2WQmD9S4T3jry6cnk,2432
nltk/test/unit/test_util.py,sha256=riDVjfX9eSaukdICajmOx7sCEhezmG246AghP0FjMOA,1806
nltk/test/unit/test_wordnet.py,sha256=pJzfNAB2UnVdQNISCOj0YI_ovqGz2u_yzVI-9u8W3f0,9158
nltk/test/unit/translate/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
nltk/test/unit/translate/__pycache__/__init__.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_bleu.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_gdfa.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm1.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm2.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm3.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm4.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm5.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_ibm_model.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_meteor.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_nist.cpython-312.pyc,,
nltk/test/unit/translate/__pycache__/test_stack_decoder.cpython-312.pyc,,
nltk/test/unit/translate/test_bleu.py,sha256=_2vgsTalBgdL2wcR7pgUhDVM7J80bYyQJHFEVPKkcHU,15825
nltk/test/unit/translate/test_gdfa.py,sha256=n2PrEqoNvmrlfO1KBDJk0Hyxh4_T4mGakBUYqaPnQhg,4616
nltk/test/unit/translate/test_ibm1.py,sha256=JRjD9LIjVYbvRZUjmFPq0H2VnDjMQsJc5BL_Pne7gF8,2596
nltk/test/unit/translate/test_ibm2.py,sha256=7RSYKeE7KsgnQFfbJ3dv9nCJpldJGLRvBoNrHdmqgOE,3291
nltk/test/unit/translate/test_ibm3.py,sha256=UG0H9W9kydRTV99nRLs8HuUzsPcbF7CwReSBM0GzqiI,4084
nltk/test/unit/translate/test_ibm4.py,sha256=vN_-dwdeNzXvyEY6okfEMuOth41wbBfcYX7AjlnAydA,5089
nltk/test/unit/translate/test_ibm5.py,sha256=rAEGJZKTRyaooEu7Ao-HWE6amCrMd_tfKqdSvF8IURM,6601
nltk/test/unit/translate/test_ibm_model.py,sha256=F18whTZi_Py1HvXgikqqiCY5gj2t83piwiqQttWz-gA,9407
nltk/test/unit/translate/test_meteor.py,sha256=_TTTgX-pwtzPyLkhOwYGmUrDf5frfm2u7OekFrqcYIg,730
nltk/test/unit/translate/test_nist.py,sha256=JUUCPuhr2zXCNjcrabnf0AjtIitXqEnZH26YgftkX8g,1609
nltk/test/unit/translate/test_stack_decoder.py,sha256=b4cXjFm2RzAXHHbcTxJYiv5nMCnscg6cy9LYX7V4KL0,9706
nltk/test/util.doctest,sha256=S0KlsilMC-MlytE2awMkmMFBy7Q8ydBthEdddOhXf7Y,1011
nltk/test/wordnet.doctest,sha256=AVVAQv_Fx-PBrbmLlntp8RvPz-QHKFDU_zIM9KMrr58,29913
nltk/test/wordnet_lch.doctest,sha256=RBNevIWUgPAkXz7CGXcQdu2VRkbOG45KRpZ9fiItDKA,2308
nltk/test/wsd.doctest,sha256=jwxVtEyZhVXhjP8XJUg7vEbEzqMKeaGQtKB0PoD-87E,2946
nltk/text.py,sha256=jTfU54KoQjRMPbO4vpzHEDn-JDSgmKZypx_eqkFwLJQ,28352
nltk/tgrep.py,sha256=yUyCH2QR5dN8-9WnsK5EdUl-6aFALSKLwCMNgk147IY,36873
nltk/tokenize/__init__.py,sha256=i51P0SAyMktKhY25uyPAVoH6wFk-yQlw9p12_shpfm4,5421
nltk/tokenize/__pycache__/__init__.cpython-312.pyc,,
nltk/tokenize/__pycache__/api.cpython-312.pyc,,
nltk/tokenize/__pycache__/casual.cpython-312.pyc,,
nltk/tokenize/__pycache__/destructive.cpython-312.pyc,,
nltk/tokenize/__pycache__/legality_principle.cpython-312.pyc,,
nltk/tokenize/__pycache__/mwe.cpython-312.pyc,,
nltk/tokenize/__pycache__/nist.cpython-312.pyc,,
nltk/tokenize/__pycache__/punkt.cpython-312.pyc,,
nltk/tokenize/__pycache__/regexp.cpython-312.pyc,,
nltk/tokenize/__pycache__/repp.cpython-312.pyc,,
nltk/tokenize/__pycache__/sexpr.cpython-312.pyc,,
nltk/tokenize/__pycache__/simple.cpython-312.pyc,,
nltk/tokenize/__pycache__/sonority_sequencing.cpython-312.pyc,,
nltk/tokenize/__pycache__/stanford.cpython-312.pyc,,
nltk/tokenize/__pycache__/stanford_segmenter.cpython-312.pyc,,
nltk/tokenize/__pycache__/texttiling.cpython-312.pyc,,
nltk/tokenize/__pycache__/toktok.cpython-312.pyc,,
nltk/tokenize/__pycache__/treebank.cpython-312.pyc,,
nltk/tokenize/__pycache__/util.cpython-312.pyc,,
nltk/tokenize/api.py,sha256=2i0dB1-5e55Slei7dDmZsS1Yb4BenO-AEcJ5EyvfInY,2274
nltk/tokenize/casual.py,sha256=XnhTJDsHTvlQz5bl9tJHI7TF8Vrc2fiZEx-8eeZD7Zs,15643
nltk/tokenize/destructive.py,sha256=JBFxNHX0RcUGSUm5eqqEf0m-RJEOIjjpTEIHc-sb_PI,9247
nltk/tokenize/legality_principle.py,sha256=-Jhlcei9b2TS7-MbXaY-uQPn1hUNX9YyTr1tShxM0rc,6089
nltk/tokenize/mwe.py,sha256=E5TDRt0dpud01iLYMfolVyH700ikZRuPsuHIW1Z1Lko,4057
nltk/tokenize/nist.py,sha256=6hSWQ82GxJI56TQwb5gwg4wvEecO2SfPUW3DpQgOki0,7541
nltk/tokenize/punkt.py,sha256=pltEdxiLBdrElsR2X09SSQgcl33odrx9e-6kz7N5LVg,69068
nltk/tokenize/regexp.py,sha256=Ngta1DgtQj1xaDJc6-6KUngHkixAA5UWxpeY0E38Mb0,8111
nltk/tokenize/repp.py,sha256=6c8F74QyGqFHNIVWEK_fFJEZeRYTSKAKn2D6oq3KVS0,8096
nltk/tokenize/sexpr.py,sha256=Sy74fS8OM_0XbuIQO-sSaWdIsk5FRnT3X_1ZlauP5MQ,5162
nltk/tokenize/simple.py,sha256=xLbG7pSmFOe-XewHOCB2n3j36pGb5i2foIPtaSDG3pU,5262
nltk/tokenize/sonority_sequencing.py,sha256=7Tx18dswhM2SLFD4Hme0ooTVfIZ_vP8uTV2ntDa6Hsk,7545
nltk/tokenize/stanford.py,sha256=y1qRDQ9gB3I1FWjpRd6rOQRS2KUx2Vc1kSWtO040wB0,3760
nltk/tokenize/stanford_segmenter.py,sha256=bAmK2ainRmSmKYPdznnHZVULA54c0IhMKewTEOm5uXk,9565
nltk/tokenize/texttiling.py,sha256=GqQrQ0rqeF5x5esFDaCyqNP3oe-ro9vtbAcE4ufNEtc,16467
nltk/tokenize/toktok.py,sha256=k2PFsMvR_eq2MkZj7xU4hXG4l97osKc1vA148z7g37Q,7515
nltk/tokenize/treebank.py,sha256=qRWoWEScUQG5REeKyASEKTgp8AEl5ufHJctnEOOHork,16267
nltk/tokenize/util.py,sha256=mSC1QCgpCfK86M70KffxJ0ze91iqdfmTjJ3Z1upCdzk,10044
nltk/toolbox.py,sha256=kH2TqjMNdIe5kaUphOdKlRDtOGkv1c4KP1TxPuRrzHw,17813
nltk/translate/__init__.py,sha256=v8fEO4ToBm5e1M1uECeTh9k7YTwbrTu1YD1LVI8VdS8,1299
nltk/translate/__pycache__/__init__.cpython-312.pyc,,
nltk/translate/__pycache__/api.cpython-312.pyc,,
nltk/translate/__pycache__/bleu_score.cpython-312.pyc,,
nltk/translate/__pycache__/chrf_score.cpython-312.pyc,,
nltk/translate/__pycache__/gale_church.cpython-312.pyc,,
nltk/translate/__pycache__/gdfa.cpython-312.pyc,,
nltk/translate/__pycache__/gleu_score.cpython-312.pyc,,
nltk/translate/__pycache__/ibm1.cpython-312.pyc,,
nltk/translate/__pycache__/ibm2.cpython-312.pyc,,
nltk/translate/__pycache__/ibm3.cpython-312.pyc,,
nltk/translate/__pycache__/ibm4.cpython-312.pyc,,
nltk/translate/__pycache__/ibm5.cpython-312.pyc,,
nltk/translate/__pycache__/ibm_model.cpython-312.pyc,,
nltk/translate/__pycache__/meteor_score.cpython-312.pyc,,
nltk/translate/__pycache__/metrics.cpython-312.pyc,,
nltk/translate/__pycache__/nist_score.cpython-312.pyc,,
nltk/translate/__pycache__/phrase_based.cpython-312.pyc,,
nltk/translate/__pycache__/ribes_score.cpython-312.pyc,,
nltk/translate/__pycache__/stack_decoder.cpython-312.pyc,,
nltk/translate/api.py,sha256=C2JuQbHSkKVznNvXARhIGTDgUph1BC7h4VpzuyWY5J4,10836
nltk/translate/bleu_score.py,sha256=osUjwmTNEm1z5wHeCJisWIERZntHKAx8n4jHThHQcSs,30631
nltk/translate/chrf_score.py,sha256=UGOT-huPiwD-oS2m_Z8MG1WepKzOCS18H9CRmENKoec,8745
nltk/translate/gale_church.py,sha256=5-MiiNZ2cMofFneTC1FBV3jiIfGojd5HjNKMsMi7I8g,8469
nltk/translate/gdfa.py,sha256=iRKZp4QGy9yS5loPzW3eKNDiczqovhFE1UYeCRLRJkQ,6108
nltk/translate/gleu_score.py,sha256=vGTCmUcO3rMlO7kbiFF1pACewz_D9ly16PKXnKfR1rk,8641
nltk/translate/ibm1.py,sha256=9_b5SREOl_x_MTFgRgM0mmOnFwV9iRjoKA8VUK2kh5M,9265
nltk/translate/ibm2.py,sha256=4BIaACgFhS0nSL6zhxRsTxCZBmXwJBK-by-v5Zjk8Bc,12224
nltk/translate/ibm3.py,sha256=aNP4i2ZVQn2U5XFRim_aEMivEqnaWRH14dj_HMVtpSA,13796
nltk/translate/ibm4.py,sha256=f_nYsoFx_se4Bfzcx8OZN7bz_8hrIgSkzKvUh5DVCHo,20251
nltk/translate/ibm5.py,sha256=wz_qX8jaVTsXdm9Ar4uaxr4IxiT2_lRSiGjvIgNq2c0,27248
nltk/translate/ibm_model.py,sha256=CW_aWDhRuchJQphDb2xDFe8junFxlgbJtWu7tC8APcs,19931
nltk/translate/meteor_score.py,sha256=8xHgkOPT3QXxRamssPlIxQyUwZNterb7dDEWITPaz1I,16892
nltk/translate/metrics.py,sha256=NPAcfoTpU-6g2gRGYV4DUYufYCbZILrdADPY1v69PQ4,1472
nltk/translate/nist_score.py,sha256=FKas6SQMPnamQTxlHSiG62_U2_p5pbygkdhBPxY6YO8,7953
nltk/translate/phrase_based.py,sha256=Zm5FIYb7Vqd-tj2llGwFvvC9ELz24lOe-f0WC2XDLqM,7667
nltk/translate/ribes_score.py,sha256=Fp0zA8hTTSdkP4L7ki5ZrOsZ5HX7X-GY-3gJHFju4Jw,13697
nltk/translate/stack_decoder.py,sha256=Yan7wOEZ0JAl5eVvFGaCSJAZoJwasgVjBz2DE6DNuKQ,20001
nltk/tree/__init__.py,sha256=jKJyLnjEOGTGIxornkOaazS9bR4F1cD0uCMST7GX3Gg,1414
nltk/tree/__pycache__/__init__.cpython-312.pyc,,
nltk/tree/__pycache__/immutable.cpython-312.pyc,,
nltk/tree/__pycache__/parented.cpython-312.pyc,,
nltk/tree/__pycache__/parsing.cpython-312.pyc,,
nltk/tree/__pycache__/prettyprinter.cpython-312.pyc,,
nltk/tree/__pycache__/probabilistic.cpython-312.pyc,,
nltk/tree/__pycache__/transforms.cpython-312.pyc,,
nltk/tree/__pycache__/tree.cpython-312.pyc,,
nltk/tree/immutable.py,sha256=91WPobJluPW9ZiQTKpUz_UwGN9vIQcg9qdvG2RoxHaY,4054
nltk/tree/parented.py,sha256=VZ0dgWAiTmW1Ls1rqE1gWIw87Xbn3-G3HE19peYKxOY,22602
nltk/tree/parsing.py,sha256=nRlwZEriE4fdKHsi2HkotDlSUGYQ3F-9IaL-dgHrfyc,2017
nltk/tree/prettyprinter.py,sha256=sZpDM21alEUBMbLm10qBQ1hIsTgZbj5FetvKL0xkLv4,24959
nltk/tree/probabilistic.py,sha256=g2Z-4JdfcuQplYKsH04xt1bpTb-MBBi-RV_ldoFx6C4,2418
nltk/tree/transforms.py,sha256=16Xb5-YFf8NC7vJIqf9MnC2HmE90Omb9x94BzXMqEVI,13350
nltk/tree/tree.py,sha256=bdq1YCkI43xMFwSZjyj-EQQRhxxx79G1P23W7ZuKtlg,35518
nltk/treeprettyprinter.py,sha256=PC-_M9rto7LNn278R1P_pd7Ol63uzgxQ8PGATUaXTeg,947
nltk/treetransforms.py,sha256=4P47I53aKBgh87UuL5ZXyqVHuHCFZwqScPC4IVODnA0,5162
nltk/twitter/__init__.py,sha256=PcVj-kua-Zy365rGyS7mxkViBi0Oc80VvTv3tOHahmA,784
nltk/twitter/__pycache__/__init__.cpython-312.pyc,,
nltk/twitter/__pycache__/api.cpython-312.pyc,,
nltk/twitter/__pycache__/common.cpython-312.pyc,,
nltk/twitter/__pycache__/twitter_demo.cpython-312.pyc,,
nltk/twitter/__pycache__/twitterclient.cpython-312.pyc,,
nltk/twitter/__pycache__/util.cpython-312.pyc,,
nltk/twitter/api.py,sha256=6gyKoKzjFWQ7xgYZTiykR3BThH3EAI00DItDvCytowI,4547
nltk/twitter/common.py,sha256=XPWxQPrVMMjwQInZHlQNIbs6EW87XDRXIwM15WCeXRQ,9850
nltk/twitter/twitter_demo.py,sha256=A_8-h1U92rbAvL2VaKEssZbvpyQ7ED2zv18yW2zBbsM,8003
nltk/twitter/twitterclient.py,sha256=A6h6y9qOHmCTliXObnx7Fr3-wZbPYA27GPHWSpFEaO4,19361
nltk/twitter/util.py,sha256=dnj_eoU8XJ1LNzgzneLUktnQGCZz6WANucUgsH_xDz0,4399
nltk/util.py,sha256=68ztt2z9oD8cnSL5MeNUwPQ68qwmQG0ncuwuBIGIN_o,43801
nltk/wsd.py,sha256=S0Upw0ON5nomrxrS4pphv-VbthCBhfzHI2TKLi76qZs,1800
