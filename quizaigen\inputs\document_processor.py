"""
Document Processor

This module handles processing of various document formats including PDF and Word files.
"""

import os
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

from .base import BaseInputProcessor


class DocumentProcessor(BaseInputProcessor):
    """Processor for document files (PDF, Word, etc.)."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize DocumentProcessor.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Supported file extensions
        self.supported_extensions = {'.pdf', '.docx', '.doc'}
        
        # Check available libraries
        if not PDF_AVAILABLE:
            self.log_warning("PyPDF2 not available - PDF processing disabled")
            self.supported_extensions.discard('.pdf')
        
        if not DOCX_AVAILABLE:
            self.log_warning("python-docx not available - Word processing disabled")
            self.supported_extensions.discard('.docx')
            self.supported_extensions.discard('.doc')
        
        self.log_info(f"Initialized DocumentProcessor with support for: {self.supported_extensions}")
    
    def process(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Process a document file and extract text.
        
        Args:
            file_path: Path to the document file
            **kwargs: Additional processing options
        
        Returns:
            Dictionary containing extracted text and metadata
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Document file not found: {file_path}")
        
        extension = file_path.suffix.lower()
        
        if extension not in self.supported_extensions:
            raise ValueError(f"Unsupported file format: {extension}")
        
        self.log_info(f"Processing document: {file_path}")
        
        try:
            if extension == '.pdf':
                return self._process_pdf(file_path, **kwargs)
            elif extension in ['.docx', '.doc']:
                return self._process_word(file_path, **kwargs)
            else:
                raise ValueError(f"Unsupported file format: {extension}")
        
        except Exception as e:
            self.log_error(f"Error processing document {file_path}: {str(e)}")
            raise
    
    def _process_pdf(self, file_path: Path, **kwargs) -> Dict[str, Any]:
        """
        Process a PDF file and extract text.
        
        Args:
            file_path: Path to PDF file
            **kwargs: Additional options
        
        Returns:
            Dictionary with extracted text and metadata
        """
        if not PDF_AVAILABLE:
            raise ImportError("PyPDF2 is required for PDF processing")
        
        text_content = []
        metadata = {
            'file_path': str(file_path),
            'file_type': 'pdf',
            'file_size': file_path.stat().st_size,
            'pages': 0,
            'extraction_method': 'PyPDF2'
        }
        
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                metadata['pages'] = len(pdf_reader.pages)
                
                # Extract text from each page
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content.append(page_text)
                        
                        self.log_debug(f"Extracted text from page {page_num + 1}")
                    
                    except Exception as e:
                        self.log_warning(f"Error extracting text from page {page_num + 1}: {str(e)}")
                        continue
                
                # Combine all text
                full_text = '\n\n'.join(text_content)
                
                # Clean and validate text
                cleaned_text = self._clean_extracted_text(full_text)
                
                if not cleaned_text.strip():
                    self.log_warning("No text content extracted from PDF")
                
                metadata['character_count'] = len(cleaned_text)
                metadata['word_count'] = len(cleaned_text.split())
                
                return {
                    'text': cleaned_text,
                    'metadata': metadata,
                    'success': True
                }
        
        except Exception as e:
            self.log_error(f"Error reading PDF file: {str(e)}")
            return {
                'text': '',
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }
    
    def _process_word(self, file_path: Path, **kwargs) -> Dict[str, Any]:
        """
        Process a Word document and extract text.
        
        Args:
            file_path: Path to Word document
            **kwargs: Additional options
        
        Returns:
            Dictionary with extracted text and metadata
        """
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx is required for Word document processing")
        
        metadata = {
            'file_path': str(file_path),
            'file_type': 'word',
            'file_size': file_path.stat().st_size,
            'paragraphs': 0,
            'extraction_method': 'python-docx'
        }
        
        try:
            # Load the document
            doc = Document(file_path)
            
            # Extract text from paragraphs
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            metadata['paragraphs'] = len(text_content)
            
            # Extract text from tables if present
            table_text = []
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_text.append(' | '.join(row_text))
            
            if table_text:
                text_content.extend(table_text)
                metadata['tables'] = len(doc.tables)
            
            # Combine all text
            full_text = '\n\n'.join(text_content)
            
            # Clean and validate text
            cleaned_text = self._clean_extracted_text(full_text)
            
            if not cleaned_text.strip():
                self.log_warning("No text content extracted from Word document")
            
            metadata['character_count'] = len(cleaned_text)
            metadata['word_count'] = len(cleaned_text.split())
            
            return {
                'text': cleaned_text,
                'metadata': metadata,
                'success': True
            }
        
        except Exception as e:
            self.log_error(f"Error reading Word document: {str(e)}")
            return {
                'text': '',
                'metadata': metadata,
                'success': False,
                'error': str(e)
            }
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean extracted text from documents.
        
        Args:
            text: Raw extracted text
        
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        import re
        
        # Replace multiple spaces with single space
        text = re.sub(r' +', ' ', text)
        
        # Replace multiple newlines with double newline
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        
        # Remove leading/trailing whitespace from each line
        lines = [line.strip() for line in text.split('\n')]
        text = '\n'.join(lines)
        
        # Remove empty lines at start and end
        text = text.strip()
        
        return text
    
    def validate_file(self, file_path: str) -> bool:
        """
        Validate if a file can be processed.
        
        Args:
            file_path: Path to the file
        
        Returns:
            True if file can be processed
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return False
            
            if not file_path.is_file():
                return False
            
            extension = file_path.suffix.lower()
            if extension not in self.supported_extensions:
                return False
            
            # Check file size (limit to 50MB)
            max_size = 50 * 1024 * 1024  # 50MB
            if file_path.stat().st_size > max_size:
                self.log_warning(f"File too large: {file_path.stat().st_size} bytes")
                return False
            
            return True
        
        except Exception as e:
            self.log_error(f"Error validating file {file_path}: {str(e)}")
            return False
    
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported file formats.
        
        Returns:
            List of supported file extensions
        """
        return list(self.supported_extensions)
    
    def process_pdf(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process PDF files.
        
        Args:
            file_path: Path to PDF file
            **kwargs: Additional options
        
        Returns:
            Processing result
        """
        return self.process(file_path, **kwargs)
    
    def process_word(self, file_path: str, **kwargs) -> Dict[str, Any]:
        """
        Convenience method to process Word documents.
        
        Args:
            file_path: Path to Word document
            **kwargs: Additional options
        
        Returns:
            Processing result
        """
        return self.process(file_path, **kwargs)
