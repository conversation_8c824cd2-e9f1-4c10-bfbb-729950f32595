"""
Question Paraphraser

This module paraphrases existing questions to create variations.
"""

import re
import random
from typing import List, Dict, Any, Optional

from .base import BaseQuestionGenerator, Question
from ..utils.text_utils import clean_text


class QuestionParaphraser(BaseQuestionGenerator):
    """Generator for paraphrasing existing questions."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize QuestionParaphraser.
        
        Args:
            config: Configuration dictionary
        """
        super().__init__(config)
        
        # Paraphrasing patterns for different question types
        self.mcq_patterns = [
            ("What is", "Which of the following describes"),
            ("What are", "Which of the following are"),
            ("Which", "What"),
            ("How many", "What is the number of"),
            ("When did", "At what time did"),
            ("Where is", "What is the location of"),
            ("Who is", "Which person is"),
            ("Why does", "What is the reason that"),
            ("How does", "In what way does")
        ]
        
        self.boolean_patterns = [
            ("Is it true that", "Does"),
            ("Is", "Can we say that"),
            ("Are", "Is it correct that"),
            ("Does", "Is it true that"),
            ("Can", "Is it possible that"),
            ("Will", "Is it likely that"),
            ("Should", "Is it advisable that")
        ]
        
        self.faq_patterns = [
            ("What is", "Can you explain what"),
            ("How do", "What is the process to"),
            ("Why is", "What makes"),
            ("When should", "At what point should"),
            ("Where can", "What is the best place to"),
            ("Who should", "Which person should"),
            ("What are the benefits", "How does it help"),
            ("What are the steps", "How do you")
        ]
        
        self.log_info("Initialized QuestionParaphraser")

    def _get_question_type(self) -> str:
        """Get the question type identifier."""
        return "paraphrase"

    def paraphrase_questions(self, questions: List[Question],
                           num_variations: int = 1) -> List[Question]:
        """
        Paraphrase a list of questions.
        
        Args:
            questions: List of questions to paraphrase
            num_variations: Number of variations per question
        
        Returns:
            List of paraphrased questions
        """
        paraphrased = []
        
        for question in questions:
            variations = self._paraphrase_single_question(question, num_variations)
            paraphrased.extend(variations)
        
        self.log_info(f"Generated {len(paraphrased)} paraphrased questions from {len(questions)} originals")
        return paraphrased
    
    def _paraphrase_single_question(self, question: Question, 
                                  num_variations: int) -> List[Question]:
        """
        Paraphrase a single question.
        
        Args:
            question: Question to paraphrase
            num_variations: Number of variations to generate
        
        Returns:
            List of paraphrased questions
        """
        variations = []
        
        for i in range(num_variations):
            paraphrased = self._generate_paraphrase(question)
            if paraphrased:
                variations.append(paraphrased)
        
        return variations
    
    def _generate_paraphrase(self, question: Question) -> Optional[Question]:
        """
        Generate a paraphrase of a question.
        
        Args:
            question: Original question
        
        Returns:
            Paraphrased question or None
        """
        question_text = question.question.strip()
        question_type = question.type
        
        # Select appropriate paraphrasing strategy
        if question_type == 'mcq':
            paraphrased_text = self._paraphrase_mcq(question_text)
        elif question_type == 'boolean':
            paraphrased_text = self._paraphrase_boolean(question_text)
        elif question_type in ['short_answer', 'faq']:
            paraphrased_text = self._paraphrase_faq(question_text)
        else:
            paraphrased_text = self._paraphrase_generic(question_text)
        
        if paraphrased_text and paraphrased_text != question_text:
            # Create paraphrased question
            paraphrased_question = Question(
                question=paraphrased_text,
                answer=question.answer,
                options=question.options,
                type=question.type,
                difficulty=question.difficulty,
                confidence=max(0.3, question.confidence - 0.1),  # Slightly lower confidence
                source_sentence=question.source_sentence,
                metadata={
                    **question.metadata,
                    'paraphrased_from': question.question,
                    'paraphrase_method': f'{question_type}_pattern'
                }
            )
            return paraphrased_question
        
        return None
    
    def _paraphrase_mcq(self, question_text: str) -> Optional[str]:
        """
        Paraphrase an MCQ question.
        
        Args:
            question_text: Original question text
        
        Returns:
            Paraphrased question text or None
        """
        # Try pattern-based paraphrasing
        for old_pattern, new_pattern in self.mcq_patterns:
            if question_text.lower().startswith(old_pattern.lower()):
                # Replace the beginning pattern
                rest_of_question = question_text[len(old_pattern):].strip()
                paraphrased = f"{new_pattern}{rest_of_question}"
                return paraphrased
        
        # Try structural paraphrasing
        return self._paraphrase_structural(question_text)
    
    def _paraphrase_boolean(self, question_text: str) -> Optional[str]:
        """
        Paraphrase a boolean question.
        
        Args:
            question_text: Original question text
        
        Returns:
            Paraphrased question text or None
        """
        # Try pattern-based paraphrasing
        for old_pattern, new_pattern in self.boolean_patterns:
            if question_text.lower().startswith(old_pattern.lower()):
                rest_of_question = question_text[len(old_pattern):].strip()
                paraphrased = f"{new_pattern} {rest_of_question}"
                return paraphrased
        
        # Try negation paraphrasing for boolean questions
        if not any(neg in question_text.lower() for neg in ['not', "n't", 'never', 'no']):
            # Add negation
            if question_text.lower().startswith('is '):
                paraphrased = question_text.replace('Is ', 'Is it not true that ', 1)
                return paraphrased
            elif question_text.lower().startswith('does '):
                paraphrased = question_text.replace('Does ', 'Does not ', 1)
                return paraphrased
        
        return self._paraphrase_structural(question_text)
    
    def _paraphrase_faq(self, question_text: str) -> Optional[str]:
        """
        Paraphrase an FAQ question.
        
        Args:
            question_text: Original question text
        
        Returns:
            Paraphrased question text or None
        """
        # Try pattern-based paraphrasing
        for old_pattern, new_pattern in self.faq_patterns:
            if question_text.lower().startswith(old_pattern.lower()):
                rest_of_question = question_text[len(old_pattern):].strip()
                paraphrased = f"{new_pattern} {rest_of_question}"
                return paraphrased
        
        return self._paraphrase_structural(question_text)
    
    def _paraphrase_generic(self, question_text: str) -> Optional[str]:
        """
        Generic paraphrasing for any question type.
        
        Args:
            question_text: Original question text
        
        Returns:
            Paraphrased question text or None
        """
        return self._paraphrase_structural(question_text)
    
    def _paraphrase_structural(self, question_text: str) -> Optional[str]:
        """
        Structural paraphrasing using word substitutions and reordering.
        
        Args:
            question_text: Original question text
        
        Returns:
            Paraphrased question text or None
        """
        # Simple synonym substitutions
        synonyms = {
            'big': 'large',
            'small': 'little',
            'fast': 'quick',
            'slow': 'gradual',
            'good': 'excellent',
            'bad': 'poor',
            'important': 'significant',
            'different': 'distinct',
            'similar': 'alike',
            'many': 'numerous',
            'few': 'several',
            'often': 'frequently',
            'sometimes': 'occasionally',
            'always': 'constantly',
            'never': 'not ever',
            'because': 'since',
            'although': 'even though',
            'however': 'nevertheless',
            'therefore': 'consequently'
        }
        
        words = question_text.split()
        paraphrased_words = []
        
        for word in words:
            # Clean word for lookup
            clean_word = re.sub(r'[^\w]', '', word.lower())
            
            # Check for synonyms
            if clean_word in synonyms:
                # Preserve original capitalization and punctuation
                if word[0].isupper():
                    replacement = synonyms[clean_word].capitalize()
                else:
                    replacement = synonyms[clean_word]
                
                # Preserve punctuation
                punctuation = re.findall(r'[^\w]', word)
                if punctuation:
                    replacement += ''.join(punctuation)
                
                paraphrased_words.append(replacement)
            else:
                paraphrased_words.append(word)
        
        paraphrased = ' '.join(paraphrased_words)
        
        # Return only if actually different
        if paraphrased != question_text:
            return paraphrased
        
        return None
    
    def _generate_questions_impl(self, text: str, num_questions: int, **kwargs) -> List[Question]:
        """
        This method is not used for paraphrasing but required by base class.
        
        Args:
            text: Input text (not used)
            num_questions: Number of questions (not used)
            **kwargs: Additional parameters (not used)
        
        Returns:
            Empty list (paraphrasing works on existing questions)
        """
        self.log_warning("_generate_questions_impl called on QuestionParaphraser - use paraphrase_questions instead")
        return []
