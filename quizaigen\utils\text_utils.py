"""
QuizAIGen Text Utilities

This module provides text processing and manipulation utilities.
"""

import re
import string
from typing import List, Optional, Dict, Any
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from nltk.corpus import stopwords
from nltk.stem import WordNetLemmatizer


# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')


def clean_text(text: str, remove_extra_whitespace: bool = True, 
               remove_special_chars: bool = False, 
               lowercase: bool = False) -> str:
    """
    Clean and normalize text.
    
    Args:
        text: Input text to clean
        remove_extra_whitespace: Whether to remove extra whitespace
        remove_special_chars: Whether to remove special characters
        lowercase: Whether to convert to lowercase
    
    Returns:
        Cleaned text
    """
    if not text:
        return ""
    
    # Remove extra whitespace
    if remove_extra_whitespace:
        text = re.sub(r'\s+', ' ', text.strip())
    
    # Remove special characters (keep basic punctuation)
    if remove_special_chars:
        text = re.sub(r'[^\w\s\.\?\!\,\;\:\-\(\)]', '', text)
    
    # Convert to lowercase
    if lowercase:
        text = text.lower()
    
    return text


def extract_sentences(text: str, min_length: int = 10, 
                     max_length: int = 500, 
                     language: str = 'english') -> List[str]:
    """
    Extract sentences from text with length filtering.
    
    Args:
        text: Input text
        min_length: Minimum sentence length
        max_length: Maximum sentence length
        language: Language for sentence tokenization
    
    Returns:
        List of filtered sentences
    """
    if not text:
        return []
    
    # Tokenize into sentences
    sentences = sent_tokenize(text, language=language)
    
    # Filter by length
    filtered_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if min_length <= len(sentence) <= max_length:
            filtered_sentences.append(sentence)
    
    return filtered_sentences


def tokenize_text(text: str, remove_punctuation: bool = False,
                 remove_stopwords: bool = False,
                 lemmatize: bool = False,
                 language: str = 'english') -> List[str]:
    """
    Tokenize text into words with various preprocessing options.
    
    Args:
        text: Input text
        remove_punctuation: Whether to remove punctuation
        remove_stopwords: Whether to remove stopwords
        lemmatize: Whether to lemmatize words
        language: Language for processing
    
    Returns:
        List of tokens
    """
    if not text:
        return []
    
    # Tokenize into words
    tokens = word_tokenize(text.lower(), language=language)
    
    # Remove punctuation
    if remove_punctuation:
        tokens = [token for token in tokens if token not in string.punctuation]
    
    # Remove stopwords
    if remove_stopwords:
        stop_words = set(stopwords.words(language))
        tokens = [token for token in tokens if token not in stop_words]
    
    # Lemmatize
    if lemmatize:
        lemmatizer = WordNetLemmatizer()
        tokens = [lemmatizer.lemmatize(token) for token in tokens]
    
    return tokens


def extract_keywords(text: str, top_k: int = 10, 
                    min_word_length: int = 3) -> List[str]:
    """
    Extract keywords from text using simple frequency analysis.
    
    Args:
        text: Input text
        top_k: Number of top keywords to return
        min_word_length: Minimum word length for keywords
    
    Returns:
        List of keywords
    """
    if not text:
        return []
    
    # Tokenize and clean
    tokens = tokenize_text(
        text, 
        remove_punctuation=True, 
        remove_stopwords=True, 
        lemmatize=True
    )
    
    # Filter by length
    tokens = [token for token in tokens if len(token) >= min_word_length]
    
    # Count frequency
    word_freq = {}
    for token in tokens:
        word_freq[token] = word_freq.get(token, 0) + 1
    
    # Sort by frequency and return top k
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, freq in sorted_words[:top_k]]


def split_into_chunks(text: str, chunk_size: int = 512, 
                     overlap: int = 50) -> List[str]:
    """
    Split text into overlapping chunks.
    
    Args:
        text: Input text
        chunk_size: Maximum chunk size in characters
        overlap: Overlap size between chunks
    
    Returns:
        List of text chunks
    """
    if not text or len(text) <= chunk_size:
        return [text] if text else []
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        
        # Try to break at sentence boundary
        if end < len(text):
            # Look for sentence ending within the last 100 characters
            search_start = max(end - 100, start)
            sentence_end = -1
            
            for i in range(end, search_start, -1):
                if text[i] in '.!?':
                    sentence_end = i + 1
                    break
            
            if sentence_end > start:
                end = sentence_end
        
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        start = end - overlap
        if start >= len(text):
            break
    
    return chunks


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    Calculate simple text similarity using Jaccard similarity.
    
    Args:
        text1: First text
        text2: Second text
    
    Returns:
        Similarity score between 0 and 1
    """
    if not text1 or not text2:
        return 0.0
    
    # Tokenize both texts
    tokens1 = set(tokenize_text(text1, remove_punctuation=True, remove_stopwords=True))
    tokens2 = set(tokenize_text(text2, remove_punctuation=True, remove_stopwords=True))
    
    if not tokens1 or not tokens2:
        return 0.0
    
    # Calculate Jaccard similarity
    intersection = len(tokens1.intersection(tokens2))
    union = len(tokens1.union(tokens2))
    
    return intersection / union if union > 0 else 0.0


def extract_named_entities(text: str) -> Dict[str, List[str]]:
    """
    Extract named entities from text using simple pattern matching.
    This is a basic implementation - for production use, consider spaCy or similar.
    
    Args:
        text: Input text
    
    Returns:
        Dictionary with entity types as keys and lists of entities as values
    """
    entities = {
        'PERSON': [],
        'ORGANIZATION': [],
        'LOCATION': [],
        'DATE': [],
        'NUMBER': []
    }
    
    # Simple patterns for demonstration
    # In production, use proper NER models
    
    # Capitalized words (potential names/places)
    capitalized_words = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
    entities['PERSON'].extend(capitalized_words[:5])  # Limit to avoid noise
    
    # Numbers
    numbers = re.findall(r'\b\d+(?:\.\d+)?\b', text)
    entities['NUMBER'].extend(numbers)
    
    # Dates (simple patterns)
    dates = re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}\b', text)
    entities['DATE'].extend(dates)
    
    return entities
