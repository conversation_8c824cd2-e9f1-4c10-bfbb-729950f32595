["tests/test_ai_models.py::TestAIModelIntegration::test_bert_integration_mock", "tests/test_ai_models.py::TestAIModelIntegration::test_missing_dependencies_handling", "tests/test_ai_models.py::TestAIModelIntegration::test_model_tier_access_control", "tests/test_ai_models.py::TestAIModelIntegration::test_t5_integration_mock", "tests/test_ai_models.py::TestBaseModel::test_lightweight_model", "tests/test_ai_models.py::TestBaseModel::test_model_config_creation", "tests/test_ai_models.py::TestBaseModel::test_model_output_creation", "tests/test_ai_models.py::TestInferencePipeline::test_pipeline_initialization", "tests/test_ai_models.py::TestInferencePipeline::test_task_model_mapping", "tests/test_ai_models.py::TestModelCache::test_cache_miss", "tests/test_ai_models.py::TestModelCache::test_cache_put_get", "tests/test_ai_models.py::TestModelCache::test_cache_stats", "tests/test_ai_models.py::TestModelManager::test_model_manager_initialization", "tests/test_ai_models.py::TestModelManager::test_tier_based_model_access", "tests/test_ai_models.py::TestQualityEnhancement::test_inference_request_structure", "tests/test_ai_models.py::TestQualityEnhancement::test_quality_enhancement_structure", "tests/test_quality_control.py::TestBloomClassifier::test_batch_classification", "tests/test_quality_control.py::TestBloomClassifier::test_bloom_classifier_initialization", "tests/test_quality_control.py::TestBloomClassifier::test_bloom_distribution", "tests/test_quality_control.py::TestBloomClassifier::test_classify_analyze_question", "tests/test_quality_control.py::TestBloomClassifier::test_classify_remember_question", "tests/test_quality_control.py::TestContentFilter::test_batch_filtering", "tests/test_quality_control.py::TestContentFilter::test_content_filter_initialization", "tests/test_quality_control.py::TestContentFilter::test_filter_appropriate_question", "tests/test_quality_control.py::TestContentFilter::test_filter_inappropriate_question", "tests/test_quality_control.py::TestContentFilter::test_get_appropriate_questions", "tests/test_quality_control.py::TestDifficultyAssessor::test_assess_complex_question", "tests/test_quality_control.py::TestDifficultyAssessor::test_assess_simple_question", "tests/test_quality_control.py::TestDifficultyAssessor::test_batch_assessment", "tests/test_quality_control.py::TestDifficultyAssessor::test_difficulty_assessor_initialization", "tests/test_quality_control.py::TestDifficultyAssessor::test_difficulty_distribution", "tests/test_quality_control.py::TestDuplicateDetector::test_detect_exact_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_detect_fuzzy_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_duplicate_detector_initialization", "tests/test_quality_control.py::TestDuplicateDetector::test_duplicate_statistics", "tests/test_quality_control.py::TestDuplicateDetector::test_group_duplicates", "tests/test_quality_control.py::TestDuplicateDetector::test_remove_duplicates", "tests/test_quality_control.py::test_quality_control_integration"]