["tests/test_ai_models.py::TestAIModelIntegration::test_bert_integration_mock", "tests/test_ai_models.py::TestAIModelIntegration::test_missing_dependencies_handling", "tests/test_ai_models.py::TestAIModelIntegration::test_model_tier_access_control", "tests/test_ai_models.py::TestAIModelIntegration::test_t5_integration_mock", "tests/test_ai_models.py::TestBaseModel::test_lightweight_model", "tests/test_ai_models.py::TestBaseModel::test_model_config_creation", "tests/test_ai_models.py::TestBaseModel::test_model_output_creation", "tests/test_ai_models.py::TestInferencePipeline::test_pipeline_initialization", "tests/test_ai_models.py::TestInferencePipeline::test_task_model_mapping", "tests/test_ai_models.py::TestModelCache::test_cache_miss", "tests/test_ai_models.py::TestModelCache::test_cache_put_get", "tests/test_ai_models.py::TestModelCache::test_cache_stats", "tests/test_ai_models.py::TestModelManager::test_model_manager_initialization", "tests/test_ai_models.py::TestModelManager::test_tier_based_model_access", "tests/test_ai_models.py::TestQualityEnhancement::test_inference_request_structure", "tests/test_ai_models.py::TestQualityEnhancement::test_quality_enhancement_structure"]