#!/usr/bin/env python3
"""
Basic Test for QuizAIGen Library

This script performs basic tests to verify the library functionality.
"""

import sys
import os

# Add the current directory to the path so we can import quizaigen
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all main components can be imported."""
    print("Testing imports...")
    
    try:
        from quizaigen import QuestionGenerator, ExportManager, TextProcessor, Config
        print("✓ Main classes imported successfully")
        
        from quizaigen.core.exceptions import QuizAIGenError, ValidationError, ProcessingError
        print("✓ Exception classes imported successfully")
        
        from quizaigen.generators.mcq_generator import MCQGenerator
        from quizaigen.generators.boolean_generator import BooleanGenerator
        print("✓ Generator classes imported successfully")
        
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality of the library."""
    print("\nTesting basic functionality...")
    
    try:
        from quizaigen import QuestionGenerator
        
        # Sample text
        sample_text = """
        Python is a high-level programming language. It was created by <PERSON> 
        and first released in 1991. Python is known for its simple syntax and readability. 
        It supports multiple programming paradigms including procedural, object-oriented, 
        and functional programming.
        """
        
        # Initialize generator
        generator = QuestionGenerator()
        print("✓ QuestionGenerator initialized")
        
        # Test available question types
        question_types = generator.get_available_question_types()
        print(f"✓ Available question types: {question_types}")
        
        # Test MCQ generation
        mcq_questions = generator.generate_mcq(sample_text, num_questions=2)
        print(f"✓ Generated {len(mcq_questions)} MCQ questions")
        
        # Test Boolean generation
        boolean_questions = generator.generate_boolean(sample_text, num_questions=2)
        print(f"✓ Generated {len(boolean_questions)} Boolean questions")
        
        # Test mixed generation
        mixed_questions = generator.generate_mixed(sample_text, num_questions=3)
        print(f"✓ Generated {len(mixed_questions)} mixed questions")
        
        # Test statistics
        stats = generator.get_statistics(mixed_questions)
        print(f"✓ Generated statistics: {stats['total_questions']} total questions")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def test_export_functionality():
    """Test export functionality."""
    print("\nTesting export functionality...")
    
    try:
        from quizaigen import QuestionGenerator, ExportManager
        
        # Generate some questions
        generator = QuestionGenerator()
        sample_text = "The Earth is the third planet from the Sun. It has one natural satellite called the Moon."
        questions = generator.generate_mixed(sample_text, num_questions=2)
        
        # Test export
        export_manager = ExportManager()
        
        # Test JSON export
        export_manager.export_questions(questions, "test_output.json", format="json")
        print("✓ JSON export successful")
        
        # Test CSV export
        export_manager.export_questions(questions, "test_output.csv", format="csv")
        print("✓ CSV export successful")
        
        # Clean up test files
        import os
        for file in ["test_output.json", "test_output.csv"]:
            if os.path.exists(file):
                os.remove(file)
        
        return True
        
    except Exception as e:
        print(f"✗ Export functionality test failed: {e}")
        return False


def test_text_processing():
    """Test text processing functionality."""
    print("\nTesting text processing...")
    
    try:
        from quizaigen import TextProcessor
        
        processor = TextProcessor()
        
        # Test basic text processing
        sample_text = "  This is a test text with extra   spaces.  "
        processed = processor.process_text(sample_text)
        print(f"✓ Text processed: '{processed}'")
        
        # Test text statistics
        stats = processor.get_text_statistics(processed)
        print(f"✓ Text statistics: {stats['word_count']} words, {stats['sentence_count']} sentences")
        
        return True
        
    except Exception as e:
        print(f"✗ Text processing test failed: {e}")
        return False


def test_configuration():
    """Test configuration functionality."""
    print("\nTesting configuration...")
    
    try:
        from quizaigen import Config, DEFAULT_CONFIG
        
        # Test default config
        config = Config()
        print("✓ Default configuration loaded")
        
        # Test config access
        model_config = config.get_model_config('mcq')
        print(f"✓ MCQ model config: {model_config.get('name', 'unknown')}")
        
        processing_config = config.get_processing_config()
        print(f"✓ Processing config: max_questions = {processing_config.get('max_questions', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("QuizAIGen Basic Test Suite")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_basic_functionality,
        test_export_functionality,
        test_text_processing,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
