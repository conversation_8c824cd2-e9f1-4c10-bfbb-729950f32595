"""
Main Question Generator API

This module provides the main public API for question generation.
"""

from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from ..core.config import Config
from ..core.logger import LoggerMixin
from ..core.exceptions import ValidationError, ProcessingError
from ..generators.base import Question
from ..generators.mcq_generator import MCQGenerator
from ..generators.boolean_generator import BooleanGenerator
from ..inputs.text_processor import TextProcessor
from ..utils.validation_utils import validate_text_input, validate_positive_integer, validate_choice


class QuestionGenerator(LoggerMixin):
    """Main API class for generating questions from text."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the Question Generator.
        
        Args:
            config: Configuration object
        """
        self.config = config or Config()
        self.text_processor = TextProcessor(self.config)
        
        # Initialize generators
        self._generators = {
            'mcq': MCQGenerator(self.config),
            'boolean': BooleanGenerator(self.config),
            # Additional generators will be added here
        }
        
        self.log_info("QuestionGenerator initialized")
    
    def generate_mcq(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Multiple Choice Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of MCQ questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('mcq', text, num_questions, **kwargs)
    
    def generate_boolean(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Boolean (True/False) Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of Boolean questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('boolean', text, num_questions, **kwargs)
    
    def generate_short_answer(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Short Answer Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of Short Answer questions as dictionaries
        
        Note:
            This is a placeholder implementation. Full implementation requires
            the ShortAnswerGenerator to be implemented.
        """
        # Placeholder - will be implemented when ShortAnswerGenerator is ready
        self.log_warning("Short answer generation not yet implemented")
        return []
    
    def generate_fill_blank(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Fill-in-the-Blank Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of Fill-in-the-Blank questions as dictionaries
        
        Note:
            This is a placeholder implementation. Full implementation requires
            the FillBlankGenerator to be implemented.
        """
        # Placeholder - will be implemented when FillBlankGenerator is ready
        self.log_warning("Fill-in-the-blank generation not yet implemented")
        return []
    
    def generate_mixed(self, text: str, num_questions: int = 10, 
                      question_types: Optional[List[str]] = None, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate a mix of different question types from text.
        
        Args:
            text: Input text
            num_questions: Total number of questions to generate
            question_types: List of question types to include (default: ['mcq', 'boolean'])
            **kwargs: Additional parameters
        
        Returns:
            List of mixed questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        if question_types is None:
            question_types = ['mcq', 'boolean']
        
        # Validate question types
        available_types = list(self._generators.keys())
        for qtype in question_types:
            validate_choice(qtype, available_types, field_name="question_types")
        
        # Distribute questions across types
        questions_per_type = num_questions // len(question_types)
        remaining_questions = num_questions % len(question_types)
        
        all_questions = []
        
        for i, qtype in enumerate(question_types):
            # Add extra question to first types if there's a remainder
            type_questions = questions_per_type + (1 if i < remaining_questions else 0)
            
            if type_questions > 0:
                questions = self._generate_questions(qtype, text, type_questions, **kwargs)
                all_questions.extend(questions)
        
        # Shuffle the mixed questions
        import random
        random.shuffle(all_questions)
        
        return all_questions
    
    def generate_from_file(self, file_path: Union[str, Path], 
                          question_type: str = 'mcq', 
                          num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate questions from a file.
        
        Args:
            file_path: Path to the input file
            question_type: Type of questions to generate
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If file processing or question generation fails
        """
        # Process the file to extract text
        text = self.text_processor.process_file(file_path)
        
        # Generate questions from the extracted text
        return self._generate_questions(question_type, text, num_questions, **kwargs)
    
    def generate_from_url(self, url: str, question_type: str = 'mcq', 
                         num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate questions from a URL.
        
        Args:
            url: URL to extract content from
            question_type: Type of questions to generate
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If URL processing or question generation fails
        """
        # Process the URL to extract text
        text = self.text_processor.process_url(url)
        
        # Generate questions from the extracted text
        return self._generate_questions(question_type, text, num_questions, **kwargs)
    
    def _generate_questions(self, question_type: str, text: str, 
                          num_questions: int, **kwargs) -> List[Dict[str, Any]]:
        """
        Internal method to generate questions using the appropriate generator.
        
        Args:
            question_type: Type of questions to generate
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        # Validate inputs
        validate_choice(question_type, list(self._generators.keys()), field_name="question_type")
        text = validate_text_input(text, field_name="text")
        num_questions = validate_positive_integer(num_questions, field_name="num_questions")
        
        # Get the appropriate generator
        generator = self._generators[question_type]
        
        # Generate questions
        questions = generator.generate_questions(text, num_questions, **kwargs)
        
        # Convert to dictionaries
        return [question.to_dict() for question in questions]
    
    def get_available_question_types(self) -> List[str]:
        """
        Get list of available question types.
        
        Returns:
            List of available question type names
        """
        return list(self._generators.keys())
    
    def get_generator_info(self, question_type: str) -> Dict[str, Any]:
        """
        Get information about a specific question generator.
        
        Args:
            question_type: Type of question generator
        
        Returns:
            Dictionary with generator information
        
        Raises:
            ValidationError: If question type is not available
        """
        validate_choice(question_type, list(self._generators.keys()), field_name="question_type")
        
        generator = self._generators[question_type]
        return {
            'question_type': question_type,
            'model_info': generator.get_model_info(),
            'supported_parameters': generator.get_supported_parameters()
        }
    
    def get_all_generators_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all available generators.
        
        Returns:
            Dictionary mapping question types to their information
        """
        return {
            qtype: self.get_generator_info(qtype) 
            for qtype in self.get_available_question_types()
        }
    
    def validate_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate a list of questions.
        
        Args:
            questions: List of question dictionaries to validate
        
        Returns:
            List of valid questions
        
        Raises:
            ValidationError: If validation fails
        """
        from ..utils.validation_utils import validate_question_data
        
        valid_questions = []
        
        for i, question in enumerate(questions):
            try:
                validated_question = validate_question_data(question)
                valid_questions.append(validated_question)
            except ValidationError as e:
                self.log_warning(f"Question {i+1} failed validation: {str(e)}")
        
        return valid_questions
    
    def get_statistics(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about generated questions.
        
        Args:
            questions: List of question dictionaries
        
        Returns:
            Dictionary with statistics
        """
        if not questions:
            return {'total_questions': 0}
        
        # Count by type
        type_counts = {}
        confidence_scores = []
        difficulties = []
        
        for question in questions:
            qtype = question.get('type', 'unknown')
            type_counts[qtype] = type_counts.get(qtype, 0) + 1
            
            if 'confidence' in question and question['confidence'] is not None:
                confidence_scores.append(question['confidence'])
            
            if 'difficulty' in question:
                difficulties.append(question['difficulty'])
        
        stats = {
            'total_questions': len(questions),
            'questions_by_type': type_counts,
            'average_confidence': sum(confidence_scores) / len(confidence_scores) if confidence_scores else None,
            'difficulty_distribution': {d: difficulties.count(d) for d in set(difficulties)} if difficulties else {}
        }
        
        return stats
