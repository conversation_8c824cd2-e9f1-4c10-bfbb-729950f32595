"""
Main Question Generator API

This module provides the main public API for question generation.
"""

from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from ..core.config import Config
from ..core.logger import LoggerMixin
from ..core.exceptions import ValidationError, ProcessingError
from ..generators.base import Question
from ..generators.mcq_generator import MCQGenerator
from ..generators.boolean_generator import BooleanGenerator
from ..generators.short_answer_generator import FAQGenerator
from ..generators.fill_blank_generator import FillBlankGenerator
from ..generators.question_paraphraser import QuestionParaphraser
from ..generators.question_answerer import QuestionAnswerer
from ..inputs.text_processor import TextProcessor
from ..inputs.document_processor import DocumentProcessor
from ..inputs.url_processor import URLProcessor
from .batch_processor import BatchProcessor
from ..utils.validation_utils import validate_text_input, validate_positive_integer, validate_choice


class QuestionGenerator(LoggerMixin):
    """Main API class for generating questions from text."""
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the Question Generator.
        
        Args:
            config: Configuration object
        """
        self.config = config or Config()
        self.text_processor = TextProcessor(self.config)
        self.document_processor = DocumentProcessor(self.config)
        self.url_processor = URLProcessor(self.config)
        self.batch_processor = BatchProcessor(self.config)

        # Initialize generators
        self._generators = {
            'mcq': MCQGenerator(self.config),
            'boolean': BooleanGenerator(self.config),
            'faq': FAQGenerator(self.config),
            'fill_blank': FillBlankGenerator(self.config),
        }

        # Initialize additional components
        self.paraphraser = QuestionParaphraser(self.config)
        self.answerer = QuestionAnswerer(self.config)
        
        self.log_info("QuestionGenerator initialized")
    
    def generate_mcq(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Multiple Choice Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of MCQ questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('mcq', text, num_questions, **kwargs)
    
    def generate_boolean(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Boolean (True/False) Questions from text.
        
        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of Boolean questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('boolean', text, num_questions, **kwargs)
    
    def generate_faq(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate FAQ Questions from text.

        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters

        Returns:
            List of FAQ questions as dictionaries

        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('faq', text, num_questions, **kwargs)
    
    def generate_fill_blank(self, text: str, num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate Fill-in-the-Blank Questions from text.

        Args:
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters

        Returns:
            List of Fill-in-the-Blank questions as dictionaries

        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        return self._generate_questions('fill_blank', text, num_questions, **kwargs)
    
    def generate_mixed(self, text: str, num_questions: int = 10, 
                      question_types: Optional[List[str]] = None, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate a mix of different question types from text.
        
        Args:
            text: Input text
            num_questions: Total number of questions to generate
            question_types: List of question types to include (default: ['mcq', 'boolean'])
            **kwargs: Additional parameters
        
        Returns:
            List of mixed questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        if question_types is None:
            question_types = ['mcq', 'boolean', 'faq', 'fill_blank']
        
        # Validate question types
        available_types = list(self._generators.keys())
        for qtype in question_types:
            validate_choice(qtype, available_types, field_name="question_types")
        
        # Distribute questions across types
        questions_per_type = num_questions // len(question_types)
        remaining_questions = num_questions % len(question_types)
        
        all_questions = []
        
        for i, qtype in enumerate(question_types):
            # Add extra question to first types if there's a remainder
            type_questions = questions_per_type + (1 if i < remaining_questions else 0)
            
            if type_questions > 0:
                questions = self._generate_questions(qtype, text, type_questions, **kwargs)
                all_questions.extend(questions)
        
        # Shuffle the mixed questions
        import random
        random.shuffle(all_questions)
        
        return all_questions
    
    def generate_from_file(self, file_path: Union[str, Path], 
                          question_type: str = 'mcq', 
                          num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate questions from a file.
        
        Args:
            file_path: Path to the input file
            question_type: Type of questions to generate
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If file processing or question generation fails
        """
        # Process the file to extract text
        file_path = Path(file_path)

        # Use appropriate processor based on file extension
        if file_path.suffix.lower() in ['.pdf', '.docx', '.doc']:
            result = self.document_processor.process(str(file_path))
            if not result['success']:
                raise ProcessingError(f"Failed to process file: {result.get('error', 'Unknown error')}")
            text = result['text']
        else:
            # Fall back to text processor for plain text files
            text = self.text_processor.process_file(file_path)

        # Generate questions from the extracted text
        return self._generate_questions(question_type, text, num_questions, **kwargs)
    
    def generate_from_url(self, url: str, question_type: str = 'mcq', 
                         num_questions: int = 5, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate questions from a URL.
        
        Args:
            url: URL to extract content from
            question_type: Type of questions to generate
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If URL processing or question generation fails
        """
        # Process the URL to extract text
        result = self.url_processor.process(url)
        if not result['success']:
            raise ProcessingError(f"Failed to process URL: {result.get('error', 'Unknown error')}")
        text = result['text']

        # Generate questions from the extracted text
        return self._generate_questions(question_type, text, num_questions, **kwargs)
    
    def _generate_questions(self, question_type: str, text: str, 
                          num_questions: int, **kwargs) -> List[Dict[str, Any]]:
        """
        Internal method to generate questions using the appropriate generator.
        
        Args:
            question_type: Type of questions to generate
            text: Input text
            num_questions: Number of questions to generate
            **kwargs: Additional parameters
        
        Returns:
            List of questions as dictionaries
        
        Raises:
            ValidationError: If input validation fails
            ProcessingError: If question generation fails
        """
        # Validate inputs
        validate_choice(question_type, list(self._generators.keys()), field_name="question_type")
        text = validate_text_input(text, field_name="text")
        num_questions = validate_positive_integer(num_questions, field_name="num_questions")
        
        # Get the appropriate generator
        generator = self._generators[question_type]
        
        # Generate questions
        questions = generator.generate_questions(text, num_questions, **kwargs)
        
        # Convert to dictionaries
        return [question.to_dict() for question in questions]
    
    def get_available_question_types(self) -> List[str]:
        """
        Get list of available question types.
        
        Returns:
            List of available question type names
        """
        return list(self._generators.keys())
    
    def get_generator_info(self, question_type: str) -> Dict[str, Any]:
        """
        Get information about a specific question generator.
        
        Args:
            question_type: Type of question generator
        
        Returns:
            Dictionary with generator information
        
        Raises:
            ValidationError: If question type is not available
        """
        validate_choice(question_type, list(self._generators.keys()), field_name="question_type")
        
        generator = self._generators[question_type]
        return {
            'question_type': question_type,
            'model_info': generator.get_model_info(),
            'supported_parameters': generator.get_supported_parameters()
        }
    
    def get_all_generators_info(self) -> Dict[str, Dict[str, Any]]:
        """
        Get information about all available generators.
        
        Returns:
            Dictionary mapping question types to their information
        """
        return {
            qtype: self.get_generator_info(qtype) 
            for qtype in self.get_available_question_types()
        }
    
    def validate_questions(self, questions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Validate a list of questions.
        
        Args:
            questions: List of question dictionaries to validate
        
        Returns:
            List of valid questions
        
        Raises:
            ValidationError: If validation fails
        """
        from ..utils.validation_utils import validate_question_data
        
        valid_questions = []
        
        for i, question in enumerate(questions):
            try:
                validated_question = validate_question_data(question)
                valid_questions.append(validated_question)
            except ValidationError as e:
                self.log_warning(f"Question {i+1} failed validation: {str(e)}")
        
        return valid_questions
    
    def get_statistics(self, questions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about generated questions.
        
        Args:
            questions: List of question dictionaries
        
        Returns:
            Dictionary with statistics
        """
        if not questions:
            return {'total_questions': 0}
        
        # Count by type
        type_counts = {}
        confidence_scores = []
        difficulties = []
        
        for question in questions:
            qtype = question.get('type', 'unknown')
            type_counts[qtype] = type_counts.get(qtype, 0) + 1
            
            if 'confidence' in question and question['confidence'] is not None:
                confidence_scores.append(question['confidence'])
            
            if 'difficulty' in question:
                difficulties.append(question['difficulty'])
        
        stats = {
            'total_questions': len(questions),
            'questions_by_type': type_counts,
            'average_confidence': sum(confidence_scores) / len(confidence_scores) if confidence_scores else None,
            'difficulty_distribution': {d: difficulties.count(d) for d in set(difficulties)} if difficulties else {}
        }
        
        return stats

    def process_batch(self, inputs: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Process multiple inputs in batch.

        Args:
            inputs: List of input dictionaries
            **kwargs: Additional processing options

        Returns:
            Batch processing results
        """
        return self.batch_processor.process_batch(inputs, **kwargs)

    def paraphrase_questions(self, questions: List[Dict[str, Any]],
                           num_variations: int = 3, **kwargs) -> List[Dict[str, Any]]:
        """
        Generate paraphrased variations of questions.

        Args:
            questions: List of question dictionaries
            num_variations: Number of variations per question
            **kwargs: Additional options

        Returns:
            List of paraphrased questions
        """
        # Convert dict questions to Question objects
        question_objects = []
        for q_dict in questions:
            question_objects.append(Question.from_dict(q_dict))

        # Generate paraphrases
        paraphrased = self.paraphraser.paraphrase_questions(
            question_objects, num_variations, **kwargs
        )

        # Convert back to dictionaries
        return [q.to_dict() for q in paraphrased]

    def answer_questions(self, context: str, questions: List[Union[str, Dict[str, Any]]],
                        **kwargs) -> List[Dict[str, Any]]:
        """
        Answer questions based on provided context.

        Args:
            context: Context text for answering questions
            questions: List of questions (strings or dictionaries)
            **kwargs: Additional options

        Returns:
            List of answers with confidence scores
        """
        return self.answerer.answer_questions(context, questions, **kwargs)
