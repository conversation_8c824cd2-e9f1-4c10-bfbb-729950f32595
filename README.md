# QuizAIGen 🧠✨

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PyPI version](https://badge.fury.io/py/quizaigen.svg)](https://badge.fury.io/py/quizaigen)
[![Documentation Status](https://readthedocs.org/projects/quizaigen/badge/?version=latest)](https://quizaigen.readthedocs.io/en/latest/?badge=latest)

**QuizAIGen** is a powerful, modular Python library for AI-powered question generation. It leverages state-of-the-art transformer models to automatically generate various types of questions from text, making it perfect for educational applications, training materials, and assessment tools.

## 🚀 Features

- **Multiple Question Types**: Generate MCQs, Boolean questions, Short answers, Fill-in-the-blanks, and more
- **AI-Powered**: Uses advanced transformer models (T5, BERT, GPT variants) for intelligent question generation
- **Flexible Input**: Supports text, PDF, Word documents, URLs, and multimedia transcripts
- **Quality Control**: Built-in question filtering and quality assessment
- **Export Options**: Multiple export formats (JSON, CSV, QTI, Moodle XML)
- **Batch Processing**: Efficient bulk question generation
- **Extensible**: Modular architecture for easy customization and extension
- **Integration-Ready**: Clean APIs designed for easy integration into applications

## 📦 Installation

### From PyPI (Recommended)
```bash
pip install quizaigen
```

### From Source
```bash
git clone https://github.com/quizaigen/quizaigen.git
cd quizaigen
pip install -e .
```

### Development Installation
```bash
git clone https://github.com/quizaigen/quizaigen.git
cd quizaigen
pip install -e ".[dev,docs,examples]"
```

## 🏃‍♂️ Quick Start

```python
from quizaigen import QuestionGenerator

# Initialize the generator
generator = QuestionGenerator()

# Generate questions from text
text = """
Artificial intelligence (AI) is intelligence demonstrated by machines, 
in contrast to the natural intelligence displayed by humans and animals. 
Leading AI textbooks define the field as the study of "intelligent agents": 
any device that perceives its environment and takes actions that maximize 
its chance of successfully achieving its goals.
"""

# Generate multiple choice questions
mcq_questions = generator.generate_mcq(text, num_questions=3)

# Generate boolean questions  
boolean_questions = generator.generate_boolean(text, num_questions=2)

# Generate short answer questions
short_answer_questions = generator.generate_short_answer(text, num_questions=2)

print("Generated Questions:")
for question in mcq_questions:
    print(f"Q: {question['question']}")
    print(f"Options: {question['options']}")
    print(f"Answer: {question['answer']}")
    print()
```

## 📚 Documentation

- **[Installation Guide](docs/installation.md)**: Detailed installation instructions
- **[API Reference](docs/api.md)**: Complete API documentation
- **[Examples](examples/)**: Code examples and tutorials
- **[Architecture](ARCHITECTURE.md)**: Library architecture and design principles

## 🎯 Supported Question Types

| Question Type | Description | Status |
|---------------|-------------|---------|
| **Multiple Choice (MCQ)** | Questions with multiple options and distractors | ✅ |
| **Boolean (Yes/No)** | True/False questions | ✅ |
| **Short Answer** | FAQ-style questions | ✅ |
| **Fill-in-the-Blank** | Questions with missing words | ✅ |
| **Paraphrasing** | Rephrased versions of existing questions | ✅ |
| **Question Answering** | Extractive and boolean QA | ✅ |

## 🔧 Configuration

QuizAIGen can be configured through a simple configuration system:

```python
from quizaigen import Config

config = Config({
    'models': {
        'mcq': {'name': 't5-base', 'cache': True},
        'boolean': {'name': 'bert-base-uncased', 'cache': True}
    },
    'processing': {
        'max_questions': 50,
        'min_quality_score': 0.7,
        'remove_duplicates': True
    }
})

generator = QuestionGenerator(config=config)
```

## 📊 Export Formats

Export your generated questions in various formats:

```python
from quizaigen import ExportManager

exporter = ExportManager()

# Export to JSON
exporter.export_json(questions, 'quiz.json')

# Export to CSV
exporter.export_csv(questions, 'quiz.csv')

# Export to QTI format
exporter.export_qti(questions, 'quiz.xml')

# Export to Moodle XML
exporter.export_moodle(questions, 'moodle_quiz.xml')
```

## 🧪 Examples

Check out our [examples directory](examples/) for comprehensive tutorials:

- [Basic Usage](examples/basic_usage.py)
- [Batch Processing](examples/batch_processing.py)
- [Custom Configuration](examples/custom_config.py)
- [Document Processing](examples/document_processing.py)
- [Export Examples](examples/export_examples.py)

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Thanks to the Hugging Face team for their excellent transformers library
- The spaCy and NLTK teams for their NLP tools
- The open-source community for inspiration and contributions

## 📞 Support

- **Documentation**: [https://quizaigen.readthedocs.io](https://quizaigen.readthedocs.io)
- **Issues**: [GitHub Issues](https://github.com/quizaigen/quizaigen/issues)
- **Discussions**: [GitHub Discussions](https://github.com/quizaigen/quizaigen/discussions)
- **Email**: <EMAIL>

---

Made with ❤️ by the QuizAIGen Team
